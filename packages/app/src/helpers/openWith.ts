import { isWebAppPlatform } from "@/services/environment";

declare global {
  interface Window {
    OPEN_WITH_FILES?: string[] | null;
  }
}

const parseWindowOpenWithFiles = () => {
  const params = new URLSearchParams(window.location.search);
  const files = params.getAll("file");
  return files.length > 0 ? files : window.OPEN_WITH_FILES;
};

const parseIntentOpenWithFiles = async () => {
  const urls: string[] = [];
  if (urls && urls.length > 0) {
    console.log("Intent Open with URL:", urls);
    return urls
      .map((url) => {
        if (url.startsWith("file://")) {
          return decodeURI(url.replace("file://", ""));
        } else if (url.startsWith("content://")) {
          return url;
        } else {
          console.info("Skip non-file URL:", url);
          return null;
        }
      })
      .filter((url) => url !== null) as string[];
  }
  return null;
};

export const parseOpenWithFiles = async () => {
  if (isWebAppPlatform()) return [];

  let files = parseWindowOpenWithFiles();
  if (!files || files.length === 0) {
    files = await parseIntentOpenWithFiles();
  }
  return files;
};
