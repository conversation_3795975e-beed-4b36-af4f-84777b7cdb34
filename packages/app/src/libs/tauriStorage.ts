import { appConfigDir } from "@tauri-apps/api/path";
import { exists, mkdir, readTextFile, writeTextFile } from "@tauri-apps/plugin-fs";
import type { StateStorage } from "zustand/middleware";

interface PendingWrite {
  timer: ReturnType<typeof setTimeout>;
  filePath: string;
  value: string;
}

const pendingWrites = new Map<string, PendingWrite>();

const debouncedWrite = (name: string, filePath: string, value: string) => {
  const existing = pendingWrites.get(name);
  if (existing) {
    clearTimeout(existing.timer);
  }

  const timer = setTimeout(async () => {
    try {
      await writeTextFile(filePath, value);
      console.log(`Debounced write completed for ${name}:`, filePath);
      pendingWrites.delete(name);
    } catch (error) {
      console.error(`Debounced write error for ${name}:`, error);
      pendingWrites.delete(name);
    }
  }, 500);

  pendingWrites.set(name, { timer, filePath, value });
};

const flushAllWrites = async () => {
  const promises: Promise<void>[] = [];

  for (const [name, pending] of pendingWrites.entries()) {
    clearTimeout(pending.timer);

    promises.push(
      writeTextFile(pending.filePath, pending.value)
        .then(() => {
          console.log(`Flushed write for ${name}:`, pending.filePath);
        })
        .catch((error) => {
          console.error(`Flush write error for ${name}:`, error);
        }),
    );
  }

  pendingWrites.clear();
  await Promise.all(promises);
};

export { flushAllWrites };

const getStorageFile = async (name: string): Promise<string> => {
  const configDir = await appConfigDir();
  console.log("configDir", configDir);
  return `${configDir}/${name}.json`;
};

export const tauriStorage: StateStorage = {
  getItem: async (name: string): Promise<string | null> => {
    console.log("getItem called with name:", name);
    try {
      const filePath = await getStorageFile(name);

      if (!(await exists(filePath))) {
        return null;
      }

      const content = await readTextFile(filePath);
      return content;
    } catch (error) {
      console.error("Zustand getItem Error:", error);
      return null;
    }
  },

  setItem: async (name: string, value: string): Promise<void> => {
    console.log("setItem called with name:", name, "will debounce write");
    try {
      const filePath = await getStorageFile(name);
      const configDir = await appConfigDir();

      if (!(await exists(configDir))) {
        await mkdir(configDir, { recursive: true });
      }

      debouncedWrite(name, filePath, value);
    } catch (error) {
      console.error("Zustand setItem Error:", error);
    }
  },

  removeItem: async (name: string): Promise<void> => {
    console.log("removeItem called with name:", name);
  },
};
