import type { TabProperties } from "app-tabs";
import type React from "react";
import { createContext, useCallback, useContext, useState } from "react";

interface TabsContextType {
  tabs: TabProperties[];
  activeTabId: string | null;
  isLibraryActive: boolean;
  addTab: (tab: Omit<TabProperties, "active">) => void;
  removeTab: (tabId: string) => void;
  activateTab: (tabId: string) => void;
  updateTab: (tabId: string, updates: Partial<TabProperties>) => void;
  openReader: (bookIds: string[], title?: string) => void;
  navigateToLibrary: () => void;
}

const TabsContext = createContext<TabsContextType | null>(null);

export function useTabsContext() {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error("useTabsContext must be used within a TabsProvider");
  }
  return context;
}

interface TabsProviderProps {
  children: React.ReactNode;
}

export function TabsProvider({ children }: TabsProviderProps) {
  const [tabs, setTabs] = useState<TabProperties[]>([]);
  const [isLibraryActive, setIsLibraryActive] = useState<boolean>(true);

  const activeTabId = tabs.find((tab) => tab.active)?.id || null;

  const addTab = useCallback((newTab: Omit<TabProperties, "active">) => {
    setIsLibraryActive(false); // 当添加新 tab 时，library 不再激活
    setTabs((prevTabs) => {
      if (prevTabs.find((tab) => tab.id === newTab.id)) {
        return prevTabs.map((tab) => ({
          ...tab,
          active: tab.id === newTab.id,
        }));
      }

      // 将所有现有 tab 设为非激活，新 tab 设为激活
      const updatedTabs = prevTabs.map((tab) => ({ ...tab, active: false }));
      return [...updatedTabs, { ...newTab, active: true }];
    });
  }, []);

  const removeTab = useCallback((tabId: string) => {
    setTabs((prevTabs) => {
      const removedTabIndex = prevTabs.findIndex((tab) => tab.id === tabId);
      const wasActive = prevTabs[removedTabIndex]?.active;
      const tabsAfterClose = prevTabs.filter((tab) => tab.id !== tabId);

      if (wasActive) {
        if (tabsAfterClose.length > 0) {
          // 类似 Chrome 的逻辑：优先选择右边的 tab，如果没有则选择左边的
          let newActiveIndex: number;

          if (removedTabIndex < tabsAfterClose.length) {
            // 如果删除的不是最后一个，激活右边的 tab（相同索引位置）
            newActiveIndex = removedTabIndex;
          } else {
            // 如果删除的是最后一个，激活左边的 tab
            newActiveIndex = tabsAfterClose.length - 1;
          }

          if (newActiveIndex >= 0 && newActiveIndex < tabsAfterClose.length) {
            tabsAfterClose[newActiveIndex].active = true;
          }
        } else {
          // 如果没有剩余的 tab，回到 library 页面
          setIsLibraryActive(true);
        }
      }

      return tabsAfterClose;
    });
  }, []);

  const activateTab = useCallback((tabId: string) => {
    setIsLibraryActive(false); // 激活其他 tab 时，library 不再激活
    setTabs((prevTabs) => prevTabs.map((tab) => ({ ...tab, active: tab.id === tabId })));
  }, []);

  const updateTab = useCallback((tabId: string, updates: Partial<TabProperties>) => {
    setTabs((prevTabs) => prevTabs.map((tab) => (tab.id === tabId ? { ...tab, ...updates } : tab)));
  }, []);

  const openReader = useCallback(
    (bookIds: string[], title?: string) => {
      const tabId = `reader-${bookIds.join(",")}`;
      const tabTitle = title || `阅读器 - ${bookIds.length} 本书`;

      addTab({
        id: tabId,
        title: tabTitle,
        isCloseIconVisible: true,
      });
    },
    [addTab],
  );

  const navigateToLibrary = useCallback(() => {
    setIsLibraryActive(true);
    setTabs((prevTabs) => prevTabs.map((tab) => ({ ...tab, active: false })));
  }, []);

  return (
    <TabsContext.Provider
      value={{
        tabs,
        activeTabId,
        isLibraryActive,
        addTab,
        removeTab,
        activateTab,
        updateTab,
        openReader,
        navigateToLibrary,
      }}
    >
      {children}
    </TabsContext.Provider>
  );
}
