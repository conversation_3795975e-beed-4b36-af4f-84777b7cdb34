import { appDataDir } from "@tauri-apps/api/path";
import { readTextFile } from "@tauri-apps/plugin-fs";
import { create } from "zustand";

// 定义书籍 metadata 接口，对应后端 BookMetadataFile
export interface BookMetadata {
  title?: string;
  language?: string;
  published?: string;
  publisher?: string;
  author?: string | { name?: string }[] | { name?: string };
  base_dir?: string;
}

type ActiveBookState = {
  activeBookId: string | null;
  activeBookMetadata: BookMetadata | null;
  setActiveBookId: (id: string | null) => void;
};

// 读取书籍 metadata.json 文件的辅助函数
async function loadBookMetadata(bookId: string): Promise<BookMetadata | null> {
  try {
    const appDataDirPath = await appDataDir();
    const metadataPath = `${appDataDirPath}/books/${bookId}/metadata.json`;

    console.log("Attempting to load metadata from:", metadataPath);

    const metadataContent = await readTextFile(metadataPath);
    const metadata = JSON.parse(metadataContent) as BookMetadata;

    console.log("Successfully loaded book metadata:", metadata);
    return metadata;
  } catch (error) {
    console.error(`Failed to load metadata for book ${bookId}:`, error);
    return null;
  }
}

export const useActiveBookStore = create<ActiveBookState>((set, get) => ({
  activeBookId: null,
  activeBookMetadata: null,

  setActiveBookId: async (id: string | null) => {
    set({ activeBookId: id, activeBookMetadata: null });

    if (id) {
      // 异步加载 metadata，不阻塞 ID 的设置
      try {
        console.log("Loading metadata for book:", id);
        const metadata = await loadBookMetadata(id);
        // 确保当前的 activeBookId 还是我们正在加载的这个 ID
        if (get().activeBookId === id) {
          console.log("Setting activeBookMetadata:", metadata);
          set({ activeBookMetadata: metadata });
        }
      } catch (error) {
        console.error(`Error loading metadata for book ${id}:`, error);
        // 即使加载失败也要确保状态一致
        if (get().activeBookId === id) {
          set({ activeBookMetadata: null });
        }
      }
    } else {
      set({ activeBookMetadata: null });
    }
  },
}));
