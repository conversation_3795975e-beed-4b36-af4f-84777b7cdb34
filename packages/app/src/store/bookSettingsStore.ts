import type { EnvConfigType } from "@/services/environment";
import type { SystemSettings } from "@/types/settings";
import { create } from "zustand";

interface BookSettingsState {
  settings: SystemSettings;
  isFontLayoutSettingsDialogOpen: boolean;
  isFontLayoutSettingsGlobal: boolean;
  setSettings: (settings: SystemSettings) => void;
  saveSettings: (envConfig: EnvConfigType, settings: SystemSettings) => void;
  setFontLayoutSettingsDialogOpen: (open: boolean) => void;
  setFontLayoutSettingsGlobal: (global: boolean) => void;
}

export const useBookSettingsStore = create<BookSettingsState>((set) => ({
  settings: {} as SystemSettings,
  isFontLayoutSettingsDialogOpen: false,
  isFontLayoutSettingsGlobal: true,
  setSettings: (settings) => set({ settings }),
  saveSettings: async (envConfig: EnvConfigType, settings: SystemSettings) => {
    const appService = await envConfig.getAppService();
    await appService.saveSettings(settings);
  },
  setFontLayoutSettingsDialogOpen: (open) => set({ isFontLayoutSettingsDialogOpen: open }),
  setFontLayoutSettingsGlobal: (global) => set({ isFontLayoutSettingsGlobal: global }),
}));
