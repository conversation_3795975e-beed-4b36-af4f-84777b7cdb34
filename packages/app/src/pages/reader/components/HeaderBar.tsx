import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useTranslation } from "@/hooks/useTranslation";
import { useBookDataStore } from "@/store/bookDataStore";
import { useReaderStore } from "@/store/readerStore";
import { useSidebarStore } from "@/store/sidebarStore";
import type { Insets } from "@/types/misc";
import clsx from "clsx";
import { TableOfContents } from "lucide-react";
import type React from "react";
import { useRef, useState } from "react";
import SearchDropdown from "./SearchDropdown";
import SettingsDropdown from "./SettingsDropdown";
import TOCView from "./sidebar/TOCView";

interface HeaderBarProps {
  bookKey: string;
  bookTitle: string;
  isTopLeft: boolean;
  isHoveredAnim: boolean;
  gridInsets: Insets;
  onCloseBook: (bookKey: string) => void;
  onGoToLibrary: () => void;
  onSetSettingsDialogOpen: (open: boolean) => void;
  section: string;
}

const HeaderBar: React.FC<HeaderBarProps> = ({ section, bookKey }) => {
  const _ = useTranslation();
  const headerRef = useRef<HTMLDivElement>(null);
  const [isTocDropdownOpen, setIsTocDropdownOpen] = useState(false);
  const { setHoveredBookKey } = useReaderStore();
  const { getBookData } = useBookDataStore();
  const { isExpanded } = useSidebarStore();
  // Get book data to access toc
  const bookData = getBookData(bookKey.split("-")[0]!);
  const bookDoc = bookData?.bookDoc;

  const handleToggleTocDropdown = (isOpen: boolean) => {
    setIsTocDropdownOpen(isOpen);
  };

  const handleTocItemSelect = () => {
    setIsTocDropdownOpen(false);
  };

  return (
    <div className="w-full">
      <div
        ref={headerRef}
        className={clsx(
          "header-bar data-tauri-drag-region pointer-events-auto visible flex h-11.5 w-full items-center px-2 pl-4 transition-[opacity,margin-top] duration-300",
          !isExpanded && "pl-20",
        )}
        onMouseLeave={() => setHoveredBookKey("")}
      >
        <div className="z-1000 flex h-full items-center justify-start gap-x-2">
          <DropdownMenu open={isTocDropdownOpen} onOpenChange={handleToggleTocDropdown}>
            <DropdownMenuTrigger asChild>
              <button className="btn btn-ghost flex h-6 w-6 items-center justify-center rounded-full p-0">
                <TableOfContents size={18} className="text-base-content" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="max-h-[calc(100vh-5rem)] w-80 overflow-y-auto p-0"
              align="start"
              side="bottom"
              sideOffset={4}
            >
              {bookDoc?.toc ? (
                <div className="h-full">
                  <TOCView
                    toc={bookDoc.toc}
                    bookKey={bookKey}
                    autoExpand={true}
                    onItemSelect={handleTocItemSelect}
                    isVisible={isTocDropdownOpen}
                  />
                </div>
              ) : (
                <div className="p-4 text-center text-muted-foreground">{_("No table of contents available")}</div>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="data-tauri-drag-region flex min-w-0 flex-1 items-center justify-center gap-x-2 px-4 ">
          {/* <h2 className="line-clamp-1 truncate font-medium text-base text-neutral-700 dark:text-neutral-300">
            {bookTitle}
          </h2> */}
          <span className="flex-shrink-0 whitespace-nowrap text-neutral-800 dark:text-neutral-300">{section}</span>
        </div>

        <div className="z-1000 flex h-full items-center justify-end space-x-4 ps-2">
          <SearchDropdown bookKey={bookKey} />
          <SettingsDropdown bookKey={bookKey} />
          {/* <BookmarkToggler bookKey={bookKey} /> */}
        </div>
      </div>
    </div>
  );
};

export default HeaderBar;
