import { useEnv } from "@/context/EnvContext";
import { useScreenWakeLock } from "@/hooks/useScreenWakeLock";
import { useTheme } from "@/hooks/useTheme";
import { getBooksWithStatus } from "@/services/bookService";
import { useBookSettingsStore } from "@/store/bookSettingsStore";
import { useDeviceControlStore } from "@/store/deviceStore";
import { useLibraryStore } from "@/store/libraryStore";
import { useNotebookStore } from "@/store/notebookStore";
import { useReaderStore } from "@/store/readerStore";
import { useSidebarStore } from "@/store/sidebarStore";
import { useThemeStore } from "@/store/themeStore";
import type { Book } from "@/types/book";
import type { BookWithStatus } from "@/types/simpleBook";
import { eventDispatcher } from "@/utils/event";
import { mountAdditionalFonts } from "@/utils/font";
import { getLocale } from "@/utils/misc";
import { initDayjs } from "@/utils/time";
import { convertFileSrc } from "@tauri-apps/api/core";
import { appDataDir } from "@tauri-apps/api/path";
import clsx from "clsx";
import type * as React from "react";
import { Suspense, useEffect, useRef, useState } from "react";
import ReaderContent from "./ReaderContent";

// 适配器：将BookWithStatus转换为旧的Book格式以保持兼容性
async function convertBookWithStatusToBook(bookWithStatus: BookWithStatus): Promise<Book> {
  const appDataDirPath = await appDataDir();

  // 构建绝对路径
  const absoluteFilePath = bookWithStatus.filePath.startsWith("/")
    ? bookWithStatus.filePath
    : `${appDataDirPath}/${bookWithStatus.filePath}`;

  const absoluteCoverPath = bookWithStatus.coverPath
    ? bookWithStatus.coverPath.startsWith("/")
      ? bookWithStatus.coverPath
      : `${appDataDirPath}/${bookWithStatus.coverPath}`
    : undefined;

  return {
    hash: bookWithStatus.id, // 使用id作为hash以保持兼容性
    format: bookWithStatus.format,
    title: bookWithStatus.title,
    author: bookWithStatus.author,
    tags: bookWithStatus.tags,
    filePath: absoluteFilePath,
    coverImageUrl: absoluteCoverPath ? convertFileSrc(absoluteCoverPath) : undefined,
    createdAt: bookWithStatus.createdAt,
    updatedAt: bookWithStatus.updatedAt,
    progress: bookWithStatus.status
      ? [bookWithStatus.status.progressCurrent, bookWithStatus.status.progressTotal]
      : undefined,
    primaryLanguage: bookWithStatus.language,
  };
}

const Reader: React.FC<{ ids?: string }> = ({ ids }) => {
  const { envConfig, appService } = useEnv();
  const { setLibrary } = useLibraryStore();
  const { hoveredBookKey } = useReaderStore();
  const { settings, setSettings } = useBookSettingsStore();
  const { isSideBarVisible, setSideBarVisible } = useSidebarStore();
  const { isNotebookVisible, setNotebookVisible } = useNotebookStore();
  const { systemUIAlwaysHidden, showSystemUI, dismissSystemUI } = useThemeStore();
  const { acquireBackKeyInterception, releaseBackKeyInterception } = useDeviceControlStore();
  const [libraryLoaded, setLibraryLoaded] = useState(false);
  const isInitiating = useRef(false);

  useTheme({ systemUIVisible: settings.alwaysShowStatusBar, appThemeColor: "base-100" });
  useScreenWakeLock(settings.screenWakeLock);

  useEffect(() => {
    mountAdditionalFonts(document);
    initDayjs(getLocale());
  }, []);

  const handleKeyDown = (event: CustomEvent) => {
    if (event.detail.keyName === "Back") {
      setSideBarVisible(false);
      setNotebookVisible(false);
      return true;
    }
    return false;
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (!appService?.isAndroidApp) return;
    if (isSideBarVisible || isNotebookVisible) {
      acquireBackKeyInterception();
      eventDispatcher.onSync("native-key-down", handleKeyDown);
    }
    if (!isSideBarVisible && !isNotebookVisible) {
      releaseBackKeyInterception();
      eventDispatcher.offSync("native-key-down", handleKeyDown);
    }
    return () => {
      if (appService?.isAndroidApp) {
        releaseBackKeyInterception();
        eventDispatcher.offSync("native-key-down", handleKeyDown);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSideBarVisible, isNotebookVisible]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isInitiating.current) return;
    isInitiating.current = true;
    const initLibrary = async () => {
      try {
        const appService = await envConfig.getAppService();
        const settings = await appService.loadSettings();
        setSettings(settings);

        // 使用新的API获取书籍
        const booksWithStatus = await getBooksWithStatus();
        const convertedBooks = await Promise.all(booksWithStatus.map(convertBookWithStatusToBook));
        setLibrary(convertedBooks);
        setLibraryLoaded(true);
      } catch (error) {
        console.error("Error loading library for reader:", error);
        setLibraryLoaded(true);
      }
    };

    initLibrary();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (!appService?.isMobileApp) return;
    const systemUIVisible = !!hoveredBookKey || settings.alwaysShowStatusBar;
    const visible = systemUIVisible && !systemUIAlwaysHidden;
    if (visible) {
      showSystemUI();
    } else {
      dismissSystemUI();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hoveredBookKey]);

  return (
    libraryLoaded &&
    settings.globalReadSettings && (
      <div
        className={clsx(
          "reader-page select-none overflow-hidden rounded-md text-base-content",
          appService?.isLinuxApp && "window-border",
          appService?.hasRoundedWindow && "rounded-window",
        )}
      >
        <Suspense>
          <ReaderContent ids={ids} settings={settings} />
        </Suspense>
      </div>
    )
  );
};

export default Reader;
