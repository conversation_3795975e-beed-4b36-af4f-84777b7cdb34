import { useEnv } from "@/context/EnvContext";
import { useUICSS } from "@/hooks/useUICSS";
import { type BookDoc, getDirection } from "@/libs/document";
import { transformContent } from "@/services/transformService";
import { useBookDataStore } from "@/store/bookDataStore";
import { useParallelViewStore } from "@/store/parallelViewStore";
import { useReaderStore } from "@/store/readerStore";
import { useSidebarStore } from "@/store/sidebarStore";
import { useThemeStore } from "@/store/themeStore";
import type { BookConfig } from "@/types/book";
import type { Insets } from "@/types/misc";
import { type FoliateView, wrappedFoliateView } from "@/types/view";
import { getBookDirFromLanguage, getBookDirFromWritingMode } from "@/utils/book";
import { getMaxInlineSize } from "@/utils/config";
import { mountAdditionalFonts } from "@/utils/font";
import { manageSyntaxHighlighting } from "@/utils/highlightjs";
import { getViewInsets } from "@/utils/insets";
import { isCJKLang } from "@/utils/lang";
import { getDirFromUILanguage } from "@/utils/rtl";
import {
  applyFixedlayoutStyles,
  applyImageStyle,
  applyTranslationStyle,
  getStyles,
  transformStylesheet,
} from "@/utils/style";
import type React from "react";
import { useAtomValue, useSetAtom } from "jotai";
import { progressAtom, viewAtom, viewSettingsAtom } from "../../new-reader/atoms/readerAtoms";
import { type RefObject, useCallback, useEffect, useRef, useState } from "react";
import { useFoliateEvents } from "../hooks/useFoliateEvents";
import { useMouseEvent, useTouchEvent } from "../hooks/useIframeEvents";
import { usePagination } from "../hooks/usePagination";
import { useProgressAutoSave } from "../hooks/useProgressAutoSave";
import {
  handleClick,
  handleKeydown,
  handleMouseMove,
  handleMousedown,
  handleMouseup,
  handleTouchEnd,
  handleTouchMove,
  handleTouchStart,
  handleWheel,
} from "../utils/iframeEventHandlers";

declare global {
  interface Window {
    eval(script: string): void;
  }
}

const FoliateViewer: React.FC<{
  bookKey: string;
  bookDoc: BookDoc;
  config: BookConfig;
  contentInsets: Insets;
}> = ({ bookKey, bookDoc, config, contentInsets: insets }) => {
  const { getView, setView: setFoliateView, setProgress } = useReaderStore();
  const { getViewSettings, setViewSettings } = useReaderStore();
  const jotaiViewSettings = useAtomValue(viewSettingsAtom);
  const setJotaiView = useSetAtom(viewAtom);
  const setJotaiProgress = useSetAtom(progressAtom);
  const setJotaiViewSettings = useSetAtom(viewSettingsAtom);
  const { getParallels } = useParallelViewStore();
  const { getBookData } = useBookDataStore();
  const { appService } = useEnv();
  const { themeCode, isDarkMode } = useThemeStore();
  const viewSettings = jotaiViewSettings ?? getViewSettings(bookKey);
  const { setSideBarBookKey } = useSidebarStore();

  const viewRef = useRef<FoliateView | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isViewCreated = useRef(false);
  const [toastMessage, setToastMessage] = useState("");

  // 动态获取容器实际尺寸
  const getContainerDimensions = useCallback(() => {
    if (!containerRef.current) {
      // 降级到使用 window 尺寸
      const viewWidth = appService?.isMobile ? screen.width : window.innerWidth;
      const viewHeight = appService?.isMobile ? screen.height : window.innerHeight;
      return {
        width: viewWidth - insets.left - insets.right,
        height: viewHeight - insets.top - insets.bottom,
      };
    }

    const rect = containerRef.current.getBoundingClientRect();
    return {
      width: rect.width - insets.left - insets.right,
      height: rect.height - insets.top - insets.bottom,
    };
  }, [appService?.isMobile, insets]);

  // 执行实际的更新逻辑
  const performUpdate = useCallback(
    (dimensions: { width: number; height: number }) => {
      try {
        if (!viewRef.current?.renderer) {
          console.log("ViewRef or renderer not ready");
          return;
        }

        const viewSettings = jotaiViewSettings ?? getViewSettings(bookKey);
        if (!viewSettings) {
          console.log("ViewSettings not ready for", bookKey);
          return;
        }

        // 更新 max-inline-size
        const maxInlineSize = getMaxInlineSize(viewSettings);
        viewRef.current.renderer.setAttribute("max-inline-size", `${maxInlineSize}px`);

        // 更新样式
        viewRef.current.renderer.setStyles?.(getStyles(viewSettings));

        // 重新应用边距和间隙 - 添加防护性检查
        if (typeof applyMarginAndGap === "function") {
          applyMarginAndGap();
        }
      } catch (error) {
        console.error("Error in performUpdate:", error);
        // 不抛出错误，避免影响其他功能
      }
    },
    [bookKey, jotaiViewSettings, getViewSettings],
  );

  const manualUpdate = useCallback(() => {
    const dimensions = getContainerDimensions();
    performUpdate(dimensions);
  }, [getContainerDimensions, performUpdate]);

  useEffect(() => {
    const handleFoliateResize = (event: CustomEvent) => {
      const { bookIds } = event.detail;
      if (bookIds?.includes(bookKey)) {
        setTimeout(() => {
          manualUpdate();
        }, 100);
      }
    };

    window.addEventListener("foliate-resize-update", handleFoliateResize as EventListener);

    return () => {
      window.removeEventListener("foliate-resize-update", handleFoliateResize as EventListener);
    };
  }, [bookKey, manualUpdate]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const timer = setTimeout(() => setToastMessage(""), 2000);
    return () => clearTimeout(timer);
  }, [toastMessage]);

  useUICSS(bookKey);
  useProgressAutoSave(bookKey);

  const progressRelocateHandler = (event: Event) => {
    const detail = (event as CustomEvent).detail;
    setProgress(bookKey, detail.cfi, detail.tocItem, detail.section, detail.location, detail.time, detail.range);
    setJotaiProgress({
      location: detail.cfi,
      sectionHref: detail.tocItem?.href || "",
      sectionLabel: detail.tocItem?.label || "",
      sectionId: detail.tocItem?.id ?? 0,
      section: detail.section,
      pageinfo: detail.location,
      timeinfo: detail.time,
      range: detail.range,
    });
  };

  const getDocTransformHandler = ({ width, height }: { width: number; height: number }) => {
    return (event: Event) => {
      const { detail } = event as CustomEvent;
      detail.data = Promise.resolve(detail.data)
        .then((data) => {
          const viewSettings = jotaiViewSettings ?? getViewSettings(bookKey);
          if (viewSettings && detail.type === "text/css") return transformStylesheet(width, height, data);
          if (viewSettings && detail.type === "application/xhtml+xml") {
            const ctx = {
              bookKey,
              viewSettings,
              content: data,
              transformers: ["punctuation", "footnote"],
            };
            return Promise.resolve(transformContent(ctx));
          }
          return data;
        })
        .catch((e) => {
          console.error(new Error(`Failed to load ${detail.name}`, { cause: e }));
          return "";
        });
    };
  };

  const docLoadHandler = (event: Event) => {
    const detail = (event as CustomEvent).detail;
    console.log("doc index loaded:", detail.index);
    if (detail.doc) {
      const writingDir = viewRef.current?.renderer.setStyles && getDirection(detail.doc);
      const viewSettings = (jotaiViewSettings ?? getViewSettings(bookKey))!;
      const bookData = getBookData(bookKey)!;
      viewSettings.vertical = writingDir?.vertical || viewSettings.writingMode.includes("vertical") || false;
      viewSettings.rtl =
        writingDir?.rtl || getDirFromUILanguage() === "rtl" || viewSettings.writingMode.includes("rl") || false;
      setViewSettings(bookKey, { ...viewSettings });
      setJotaiViewSettings({ ...viewSettings });

      mountAdditionalFonts(detail.doc, isCJKLang(bookData.book?.primaryLanguage));

      if (bookDoc.rendition?.layout === "pre-paginated") {
        applyFixedlayoutStyles(detail.doc, viewSettings);
      }

      applyImageStyle(detail.doc);

      // only call on load if we have highlighting turned on.
      if (viewSettings.codeHighlighting) {
        manageSyntaxHighlighting(detail.doc, viewSettings);
      }

      if (!detail.doc.isEventListenersAdded) {
        // listened events in iframes are posted to the main window
        // and then used by useMouseEvent and useTouchEvent
        // and more gesture events can be detected in the iframeEventHandlers
        detail.doc.isEventListenersAdded = true;
        detail.doc.addEventListener("keydown", handleKeydown.bind(null, bookKey));
        detail.doc.addEventListener("mousedown", handleMousedown.bind(null, bookKey));
        detail.doc.addEventListener("mouseup", handleMouseup.bind(null, bookKey));
        detail.doc.addEventListener("mousemove", handleMouseMove.bind(null, bookKey));
        detail.doc.addEventListener("click", handleClick.bind(null, bookKey));
        detail.doc.addEventListener("wheel", handleWheel.bind(null, bookKey));
        detail.doc.addEventListener("touchstart", handleTouchStart.bind(null, bookKey));
        detail.doc.addEventListener("touchmove", handleTouchMove.bind(null, bookKey));
        detail.doc.addEventListener("touchend", handleTouchEnd.bind(null, bookKey));
      }
    }
  };

  const docRelocateHandler = (event: Event) => {
    const detail = (event as CustomEvent).detail;
    if (detail.reason !== "scroll" && detail.reason !== "page") return;

    const parallelViews = getParallels(bookKey);
    if (parallelViews && parallelViews.size > 0) {
      parallelViews.forEach((key) => {
        if (key !== bookKey) {
          const target = getView(key)?.renderer;
          if (target) {
            target.goTo?.({ index: detail.index, anchor: detail.fraction });
          }
        }
      });
    }
  };

  const { handlePageFlip, handleContinuousScroll } = usePagination(
    bookKey,
    viewRef,
    containerRef as RefObject<HTMLDivElement>,
  );
  const mouseHandlers = useMouseEvent(bookKey, handlePageFlip, handleContinuousScroll);
  const touchHandlers = useTouchEvent(bookKey, handleContinuousScroll);

  useFoliateEvents(viewRef.current, {
    onLoad: docLoadHandler,
    onRelocate: progressRelocateHandler,
    onRendererRelocate: docRelocateHandler,
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isViewCreated.current) return;
    isViewCreated.current = true;

    const openBook = async () => {
      console.log("Opening book", bookKey);
      setSideBarBookKey(bookKey);
      await import("foliate-js/view.js");
      const view = wrappedFoliateView(document.createElement("foliate-view") as FoliateView);
      view.id = `foliate-view-${bookKey}`;
      document.body.append(view);
      containerRef.current?.appendChild(view);

      const viewSettings = getViewSettings(bookKey)!;
      const writingMode = viewSettings.writingMode;
      if (writingMode) {
        const settingsDir = getBookDirFromWritingMode(writingMode);
        const languageDir = getBookDirFromLanguage(bookDoc.metadata.language);
        if (settingsDir !== "auto") {
          bookDoc.dir = settingsDir;
        } else if (languageDir !== "auto") {
          bookDoc.dir = languageDir;
        }
      }

      await view.open(bookDoc);
      // make sure we can listen renderer events after opening book
      viewRef.current = view;
      setFoliateView(bookKey, view);
      setJotaiView(view);

      const { book } = view;

      book.transformTarget?.addEventListener("load", (event: Event) => {
        const { detail } = event as CustomEvent;
        if (detail.isScript) {
          detail.allowScript = viewSettings.allowScript ?? false;
        }
      });
      const { width, height } = getContainerDimensions();
      book.transformTarget?.addEventListener("data", getDocTransformHandler({ width, height }));
      view.renderer.setStyles?.(getStyles(viewSettings));
      applyTranslationStyle(viewSettings);

      const animated = viewSettings.animated!;
      const maxColumnCount = viewSettings.maxColumnCount!;
      const maxInlineSize = getMaxInlineSize(viewSettings);
      const maxBlockSize = viewSettings.maxBlockSize!;
      if (animated) {
        view.renderer.setAttribute("animated", "");
      } else {
        view.renderer.removeAttribute("animated");
      }
      view.renderer.setAttribute("max-column-count", maxColumnCount);
      view.renderer.setAttribute("max-inline-size", `${maxInlineSize}px`);
      view.renderer.setAttribute("max-block-size", `${maxBlockSize}px`);
      applyMarginAndGap();

      const lastLocation = config.location;
      if (lastLocation) {
        await view.init({ lastLocation });
      } else {
        await view.goToFraction(0);
      }

      // 暴露手动更新函数，供外部调用
      manualUpdate();
    };

    openBook();

    return () => {
      if (viewRef.current) {
        try {
          const view = viewRef.current;

          // 先清理引用，避免其他地方继续使用
          viewRef.current = null;
          isViewCreated.current = false;

          // 延迟清理，避免与正在进行的渲染操作冲突
          setTimeout(() => {
            try {
              if (view.close) {
                view.close();
              }
              if (view.remove) {
                view.remove();
              }
            } catch (cleanupError) {
              console.warn("Error in delayed cleanup:", cleanupError);
            }
          }, 50);
        } catch (error) {
          console.warn("Error during foliate view cleanup:", error);
          // 即使清理失败，也要重置引用
          viewRef.current = null;
          isViewCreated.current = false;
        }
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const applyMarginAndGap = () => {
    const viewSettings = jotaiViewSettings ?? getViewSettings(bookKey);
    if (!viewSettings) {
      console.log("ViewSettings not available in applyMarginAndGap for", bookKey);
      return;
    }

    const viewInsets = getViewInsets(viewSettings);
    const showDoubleBorder = viewSettings.vertical && viewSettings.doubleBorder;
    const showDoubleBorderHeader = showDoubleBorder && viewSettings.showHeader;
    const showDoubleBorderFooter = showDoubleBorder && viewSettings.showFooter;
    const showTopHeader = viewSettings.showHeader && !viewSettings.vertical;
    const showBottomFooter = viewSettings.showFooter && !viewSettings.vertical;
    const moreTopInset = showTopHeader ? Math.max(0, 44 - insets.top) : 0;
    const moreBottomInset = showBottomFooter ? Math.max(0, 44 - insets.bottom) : 0;
    const moreRightInset = showDoubleBorderHeader ? 32 : 0;
    const moreLeftInset = showDoubleBorderFooter ? 32 : 0;
    const topMargin = (showTopHeader ? insets.top : viewInsets.top) + moreTopInset;
    const rightMargin = insets.right + moreRightInset;
    const bottomMargin = (showBottomFooter ? insets.bottom : viewInsets.bottom) + moreBottomInset;
    const leftMargin = insets.left + moreLeftInset;

    viewRef.current?.renderer.setAttribute("margin-top", `${topMargin}px`);
    viewRef.current?.renderer.setAttribute("margin-right", `${rightMargin}px`);
    viewRef.current?.renderer.setAttribute("margin-bottom", `${bottomMargin}px`);
    viewRef.current?.renderer.setAttribute("margin-left", `${leftMargin}px`);
    viewRef.current?.renderer.setAttribute("gap", `${viewSettings.gapPercent}%`);
    if (viewSettings.scrolled) {
      viewRef.current?.renderer.setAttribute("flow", "scrolled");
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (viewRef.current?.renderer) {
      const viewSettings = (jotaiViewSettings ?? getViewSettings(bookKey))!;
      viewRef.current.renderer.setStyles?.(getStyles(viewSettings));
      if (bookDoc.rendition?.layout === "pre-paginated") {
        const docs = viewRef.current.renderer.getContents();
        docs.forEach(({ doc }) => applyFixedlayoutStyles(doc, viewSettings));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [themeCode, isDarkMode, viewSettings?.overrideColor, viewSettings?.invertImgColorInDark]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (viewRef.current?.renderer && viewSettings) {
      applyMarginAndGap();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    insets.top,
    insets.right,
    insets.bottom,
    insets.left,
    viewSettings?.doubleBorder,
    viewSettings?.showHeader,
    viewSettings?.showFooter,
  ]);

  return <div ref={containerRef} className="foliate-viewer h-[100%] w-[100%]" {...mouseHandlers} {...touchHandlers} />;
};

export default FoliateViewer;
