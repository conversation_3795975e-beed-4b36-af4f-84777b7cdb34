import { Checkbox } from "@/components/ui/checkbox";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useEnv } from "@/context/EnvContext";
import { useTranslation } from "@/hooks/useTranslation";
import { CURATED_FONTS } from "@/services/constants";
import { useReaderStore } from "@/store/readerStore";
import { useThemeStore } from "@/store/themeStore";
import { getMaxInlineSize } from "@/utils/config";
import { isCJKEnv } from "@/utils/misc";
import { getStyles } from "@/utils/style";
import { Settings2 } from "lucide-react";
import type React from "react";
import { useCallback, useEffect, useState } from "react";
import { MdCheck, MdOutlineDarkMode, MdOutlineLightMode } from "react-icons/md";
import { TbSunMoon } from "react-icons/tb";
import { saveViewSettings } from "../utils/viewSettingsHelper";
import { FontSizeSlider } from "./FontSizeSlider";

interface SettingsDropdownProps {
  bookKey: string;
}

const FONT_SIZE_MIN = 12;
const FONT_SIZE_MAX = 32;
const FONT_SIZE_STEP = 2;

const SettingsDropdown: React.FC<SettingsDropdownProps> = ({ bookKey }) => {
  const _ = useTranslation();
  const { envConfig } = useEnv();
  const { getViewSettings, getView, setViewSettings } = useReaderStore();
  const { themeMode, setThemeMode } = useThemeStore();
  const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = useState(false);

  const viewSettings = getViewSettings(bookKey);
  const currentFontSize = viewSettings?.defaultFontSize || 16;

  const [fontSize, setFontSize] = useState(currentFontSize);
  const [isScrolledMode, setScrolledMode] = useState(viewSettings?.scrolled || false);
  const [isContinuousScroll, setIsContinuousScroll] = useState(viewSettings?.continuousScroll || false);

  // 计算当前选中的字体方案ID
  const currentFontId = viewSettings
    ? CURATED_FONTS.find(
        (font) =>
          font.serif === viewSettings.serifFont &&
          font.sansSerif === viewSettings.sansSerifFont &&
          font.cjk === viewSettings.defaultCJKFont,
      )?.id || "comfortable"
    : "comfortable";

  const [selectedFontId, setSelectedFontId] = useState(currentFontId);

  useEffect(() => {
    setFontSize(currentFontSize);
  }, [currentFontSize]);

  useEffect(() => {
    setSelectedFontId(currentFontId);
  }, [currentFontId]);

  useEffect(() => {
    setScrolledMode(viewSettings?.scrolled || false);
  }, [viewSettings?.scrolled]);

  useEffect(() => {
    setIsContinuousScroll(viewSettings?.continuousScroll || false);
  }, [viewSettings?.continuousScroll]);

  useEffect(() => {
    if (!viewSettings || isScrolledMode === viewSettings.scrolled) return;

    // 更新本地 viewSettings
    const updatedSettings = { ...viewSettings, scrolled: isScrolledMode };
    setViewSettings(bookKey, updatedSettings);

    // 保存到存储
    saveViewSettings(envConfig, bookKey, "scrolled", isScrolledMode);

    // 更新渲染器
    const view = getView(bookKey);
    if (view?.renderer) {
      view.renderer.setAttribute("flow", isScrolledMode ? "scrolled" : "paginated");
      view.renderer.setAttribute("max-inline-size", `${getMaxInlineSize(updatedSettings)}px`);
      view.renderer.setStyles?.(getStyles(updatedSettings));
    }
  }, [isScrolledMode, viewSettings, bookKey, envConfig, getView, setViewSettings]);

  useEffect(() => {
    if (!viewSettings || isContinuousScroll === viewSettings.continuousScroll) return;

    // 保存连续滚动设置
    saveViewSettings(envConfig, bookKey, "continuousScroll", isContinuousScroll);
  }, [isContinuousScroll, viewSettings, bookKey, envConfig]);

  const handleToggleSettingsDropdown = (isOpen: boolean) => {
    setIsSettingsDropdownOpen(isOpen);
  };

  const handleFontSizeChange = useCallback(
    (newSize: number) => {
      const clampedSize = Math.max(FONT_SIZE_MIN, Math.min(FONT_SIZE_MAX, newSize));
      setFontSize(clampedSize);
      saveViewSettings(envConfig, bookKey, "defaultFontSize", clampedSize);
    },
    [envConfig, bookKey],
  );

  const handleFontChange = async (fontId: string) => {
    setSelectedFontId(fontId);
    const selectedFont = CURATED_FONTS.find((f) => f.id === fontId);
    if (selectedFont) {
      await saveViewSettings(envConfig, bookKey, "serifFont", selectedFont.serif);
      await saveViewSettings(envConfig, bookKey, "sansSerifFont", selectedFont.sansSerif);
      await saveViewSettings(envConfig, bookKey, "defaultCJKFont", selectedFont.cjk);
    }
  };

  const handleIncrease = () => {
    handleFontSizeChange(fontSize + FONT_SIZE_STEP);
  };

  const handleDecrease = () => {
    handleFontSizeChange(fontSize - FONT_SIZE_STEP);
  };

  const isCJK = isCJKEnv();

  return (
    <DropdownMenu open={isSettingsDropdownOpen} onOpenChange={handleToggleSettingsDropdown}>
      <DropdownMenuTrigger asChild>
        <button
          className="btn btn-ghost flex h-8 min-h-8 w-8 items-center justify-center rounded-full p-0"
          title={_("Font Size Settings")}
        >
          <Settings2 size={18} />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80 p-4" align="end" side="bottom" sideOffset={4}>
        <div className="space-y-5">
          {/* 字体选择 */}
          <div>
            <div className="mb-3 font-medium text-sm">{_("Font Family")}</div>
            <div className="grid grid-cols-2 gap-2">
              {CURATED_FONTS.map((font) => (
                <button
                  key={font.id}
                  className={`rounded-lg border-2 p-3 text-left transition-all ${
                    selectedFontId === font.id
                      ? "border-neutral-500 bg-neutral-100 dark:border-neutral-400 dark:bg-neutral-800"
                      : "border-gray-200 hover:border-neutral-400 dark:border-gray-700 dark:hover:border-neutral-500"
                  }`}
                  onClick={() => handleFontChange(font.id)}
                >
                  <div className="mb-1 font-medium text-xs">{font.name}</div>
                  <div
                    className="text-sm leading-relaxed"
                    style={{
                      fontFamily: isCJK ? font.cjk : font.serif,
                      fontWeight: font.id === "classic" ? "normal" : "inherit",
                    }}
                  >
                    <div>{isCJK ? "春江潮水连海平" : "The quick brown fox"}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* 字体大小 */}
          <div>
            <div className="mb-3 font-medium text-sm">{_("Font Size")}</div>
            <div className="flex items-center justify-center gap-4">
              <button
                className="btn btn-sm size-8 cursor-pointer rounded-md border-none bg-neutral-100 hover:bg-neutral-200 disabled:bg-neutral-50 disabled:opacity-50 dark:bg-neutral-800 dark:disabled:bg-neutral-900 dark:disabled:opacity-50 dark:hover:bg-neutral-700"
                onClick={handleDecrease}
                disabled={fontSize <= FONT_SIZE_MIN}
                title={_("Decrease font size")}
              >
                <span className="font-medium text-xs">A</span>
              </button>

              <FontSizeSlider
                value={[fontSize]}
                onValueChange={(value: number[]) => handleFontSizeChange(value[0]!)}
                min={FONT_SIZE_MIN}
                max={FONT_SIZE_MAX}
                step={FONT_SIZE_STEP}
                showTooltip={true}
                tooltipContent={(value) => `${value}px`}
              />
              <button
                className="btn btn-sm size-8 cursor-pointer rounded-md border-none bg-neutral-100 hover:bg-neutral-200 disabled:bg-neutral-50 disabled:opacity-50 dark:bg-neutral-800 dark:disabled:bg-neutral-900 dark:disabled:opacity-50 dark:hover:bg-neutral-700"
                onClick={handleIncrease}
                disabled={fontSize >= FONT_SIZE_MAX}
                title={_("Increase font size")}
              >
                <span className="font-medium text-lg">A</span>
              </button>
            </div>
          </div>

          {/* 滚动模式 */}
          <div>
            <div className="mb-3 font-medium text-sm">{_("Reading Mode")}</div>
            <div className="space-y-3">
              <div className="flex items-center gap-4">
                <button
                  className={`btn btn-sm flex h-10 flex-1 items-center justify-between rounded-md border-none px-3 ${
                    !isScrolledMode
                      ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                      : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                  }`}
                  onClick={() => setScrolledMode(false)}
                  title={_("Paginated Mode")}
                >
                  <span className="text-sm">{_("Paginated")}</span>
                  {!isScrolledMode && <MdCheck size={16} />}
                </button>
                <button
                  className={`btn btn-sm flex h-10 flex-1 items-center justify-between rounded-md border-none px-3 ${
                    isScrolledMode
                      ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                      : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                  }`}
                  onClick={() => setScrolledMode(true)}
                  title={_("Scrolled Mode")}
                >
                  <span className="text-sm">{_("Scrolled")}</span>
                  {isScrolledMode && <MdCheck size={16} />}
                </button>
              </div>

              {isScrolledMode && (
                <div className="flex items-center justify-between rounded-md border border-neutral-200 bg-neutral-50 px-3 py-2 dark:border-neutral-700 dark:bg-neutral-800/50">
                  <label
                    htmlFor="continuous-scroll"
                    className="cursor-pointer text-neutral-700 text-sm dark:text-neutral-300"
                  >
                    {_("Cross-Chapter Scroll")}
                  </label>
                  <Checkbox
                    id="continuous-scroll"
                    checked={isContinuousScroll}
                    onCheckedChange={(checked) => setIsContinuousScroll(checked === "indeterminate" ? false : checked)}
                  />
                </div>
              )}
            </div>
          </div>

          {/* 主题模式 */}
          <div>
            <div className="mb-3 font-medium text-sm">{_("Theme Mode")}</div>
            <div className="flex items-center gap-4">
              <button
                className={`btn btn-sm flex size-8 items-center justify-center rounded-md border-none ${
                  themeMode === "auto"
                    ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                    : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                }`}
                onClick={() => setThemeMode("auto")}
                title={_("Auto Mode")}
              >
                <TbSunMoon size={16} />
              </button>
              <button
                className={`btn btn-sm flex size-8 items-center justify-center rounded-md border-none ${
                  themeMode === "light"
                    ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                    : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                }`}
                onClick={() => setThemeMode("light")}
                title={_("Light Mode")}
              >
                <MdOutlineLightMode size={16} />
              </button>
              <button
                className={`btn btn-sm flex size-8 items-center justify-center rounded-md border-none ${
                  themeMode === "dark"
                    ? "bg-neutral-800 text-white hover:bg-neutral-700 dark:bg-neutral-100 dark:text-neutral-900 dark:hover:bg-neutral-200"
                    : "bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-800 dark:hover:bg-neutral-700"
                }`}
                onClick={() => setThemeMode("dark")}
                title={_("Dark Mode")}
              >
                <MdOutlineDarkMode size={16} />
              </button>
            </div>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default SettingsDropdown;
