import clsx from "clsx";
import type React from "react";
import { useCallback, useEffect, useState } from "react";

import { useEnv } from "@/context/EnvContext";
import { useTranslation } from "@/hooks/useTranslation";
import { CURATED_FONTS, FONT_SIZE_PRESETS } from "@/services/constants";
import { useReaderStore } from "@/store/readerStore";
import { isCJKEnv } from "@/utils/misc";
import { saveViewSettings } from "../../utils/viewSettingsHelper";
import type { SettingsPanelPanelProp } from "./SettingsDialog";

interface FontPreviewProps {
  font: (typeof CURATED_FONTS)[0];
  isSelected: boolean;
  onClick: () => void;
}

const FontPreview: React.FC<FontPreviewProps> = ({ font, isSelected, onClick }) => {
  const isCJK = isCJKEnv();

  const previewStyle = {
    fontFamily: isCJK ? font.cjk : font.serif,
  };

  return (
    <div
      className={clsx(
        "cursor-pointer rounded-lg border-2 p-4 transition-all hover:shadow-md",
        isSelected ? "border-primary bg-primary/10" : "border-gray-200 hover:border-primary/50",
      )}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-medium text-base-content">{font.name}</h3>
          <p className="text-base-content/60 text-sm">{font.description}</p>
        </div>
        <div className="ml-4">
          <div className="text-lg leading-relaxed" style={previewStyle}>
            {isCJK ? "春江潮水连海平" : "The quick brown fox"}
          </div>
        </div>
      </div>
    </div>
  );
};

const SimplifiedFontPanel: React.FC<SettingsPanelPanelProp> = ({ bookKey, onRegisterReset }) => {
  const _ = useTranslation();
  const { envConfig } = useEnv();
  const { getViewSettings } = useReaderStore();
  const viewSettings = getViewSettings(bookKey)!;

  // 根据当前设置找到对应的精选字体
  const getCurrentFontId = () => {
    const current = CURATED_FONTS.find(
      (font) =>
        font.serif === viewSettings.serifFont &&
        font.sansSerif === viewSettings.sansSerifFont &&
        font.cjk === viewSettings.defaultCJKFont,
    );
    return current?.id || "comfortable"; // 默认为舒适阅读
  };

  const [selectedFontId, setSelectedFontId] = useState(getCurrentFontId());
  const [fontSize, setFontSize] = useState(viewSettings.defaultFontSize!);

  const handleReset = useCallback(() => {
    // 重置为默认值
    setSelectedFontId("comfortable");
    setFontSize(16);

    // 应用默认字体设置
    const defaultFont = CURATED_FONTS.find((f) => f.id === "comfortable");
    if (defaultFont) {
      saveViewSettings(envConfig, bookKey, "serifFont", defaultFont.serif);
      saveViewSettings(envConfig, bookKey, "sansSerifFont", defaultFont.sansSerif);
      saveViewSettings(envConfig, bookKey, "defaultCJKFont", defaultFont.cjk);
    }
    saveViewSettings(envConfig, bookKey, "defaultFontSize", 16);
  }, [envConfig, bookKey]);

  const handleFontChange = async (fontId: string) => {
    setSelectedFontId(fontId);
    const selectedFont = CURATED_FONTS.find((f) => f.id === fontId);
    if (selectedFont) {
      await saveViewSettings(envConfig, bookKey, "serifFont", selectedFont.serif);
      await saveViewSettings(envConfig, bookKey, "sansSerifFont", selectedFont.sansSerif);
      await saveViewSettings(envConfig, bookKey, "defaultCJKFont", selectedFont.cjk);
    }
  };

  const handleFontSizeChange = async (size: number) => {
    setFontSize(size);
    await saveViewSettings(envConfig, bookKey, "defaultFontSize", size);
  };

  useEffect(() => {
    onRegisterReset?.(handleReset);
  }, [onRegisterReset, handleReset]);

  return (
    <div className="space-y-6">
      {/* 字体大小选择 */}
      <div className="w-full">
        <h2 className="mb-3 font-medium text-base-content">{_("Font Size")}</h2>
        <div className="flex flex-wrap gap-2">
          {FONT_SIZE_PRESETS.map((preset) => (
            <button
              key={preset.size}
              className={clsx(
                "rounded-lg border px-4 py-2 transition-all",
                fontSize === preset.size
                  ? "border-primary bg-primary text-primary-content"
                  : "border-gray-200 bg-base-100 hover:border-primary/50 hover:bg-primary/10",
              )}
              onClick={() => handleFontSizeChange(preset.size)}
            >
              <span style={{ fontSize: `${Math.min(preset.size, 16)}px` }}>{preset.label}</span>
            </button>
          ))}
        </div>

        {/* 自定义字体大小滑块 */}
        <div className="mt-4">
          <div className="flex items-center gap-4">
            <span className="min-w-fit text-base-content/60 text-sm">{_("Custom Size")}</span>
            <input
              type="range"
              min="12"
              max="32"
              value={fontSize}
              onChange={(e) => handleFontSizeChange(Number(e.target.value))}
              className="range range-primary flex-1"
            />
            <span className="min-w-fit font-mono text-sm">{fontSize}px</span>
          </div>
        </div>
      </div>

      {/* 字体选择 */}
      <div className="w-full">
        <h2 className="mb-3 font-medium text-base-content">{_("Font Family")}</h2>
        <div className="space-y-3">
          {CURATED_FONTS.map((font) => (
            <FontPreview
              key={font.id}
              font={font}
              isSelected={selectedFontId === font.id}
              onClick={() => handleFontChange(font.id)}
            />
          ))}
        </div>
      </div>

      {/* 高级设置入口 */}
      <div className="w-full">
        <details className="collapse-arrow collapse rounded-lg border border-base-200">
          <summary className="collapse-title font-medium">{_("Advanced Font Settings")}</summary>
          <div className="collapse-content space-y-4">
            <p className="text-base-content/60 text-sm">
              {_("For advanced users who want to customize individual font families.")}
            </p>
            <button
              className="btn btn-outline btn-sm"
              onClick={() => {
                // 这里可以切换到完整的字体面板
                console.log("Switch to full font panel");
              }}
            >
              {_("Open Full Font Settings")}
            </button>
          </div>
        </details>
      </div>
    </div>
  );
};

export default SimplifiedFontPanel;
