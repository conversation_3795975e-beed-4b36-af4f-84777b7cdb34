import { useTranslation } from "@/hooks/useTranslation";
import { CUSTOM_THEME_TEMPLATES } from "@/services/constants";
import { useBookSettingsStore } from "@/store/bookSettingsStore";
import type { CustomTheme } from "@/styles/themes";
import { md5Fingerprint } from "@/utils/md5";
import clsx from "clsx";
import { useState } from "react";
import ColorInput from "./ColorInput";

type ThemeEditorProps = {
  customTheme: CustomTheme | null;
  onSave: (customTheme: CustomTheme) => void;
  onDelete: (customTheme: CustomTheme) => void;
  onCancel: () => void;
};

const ThemeEditor: React.FC<ThemeEditorProps> = ({ customTheme, onSave, onDelete, onCancel }) => {
  const _ = useTranslation();
  const { settings } = useBookSettingsStore();
  const template = CUSTOM_THEME_TEMPLATES[Math.floor(Math.random() * CUSTOM_THEME_TEMPLATES.length)]!;
  const [lightTextColor, setLightTextColor] = useState(customTheme?.colors.light.fg || template.light.fg);
  const [lightBackgroundColor, setLightBackgroundColor] = useState(customTheme?.colors.light.bg || template.light.bg);
  const [lightPrimaryColor, setLightPrimaryColor] = useState(
    customTheme?.colors.light.primary || template.light.primary,
  );
  const [darkTextColor, setDarkTextColor] = useState(customTheme?.colors.dark.fg || template.dark.fg);
  const [darkBackgroundColor, setDarkBackgroundColor] = useState(customTheme?.colors.dark.bg || template.dark.bg);
  const [darkPrimaryColor, setDarkPrimaryColor] = useState(customTheme?.colors.dark.primary || template.dark.primary);

  const [themeName, setThemeName] = useState(customTheme?.label || _("Custom"));

  const ThemePreview: React.FC<{
    textColor: string;
    backgroundColor: string;
    primaryColor: string;
    label: string;
  }> = ({ textColor, backgroundColor, primaryColor, label }) => (
    <div className="mt-4 mb-2">
      <label className="mb-1 block font-medium text-sm">{label}</label>
      <div
        className="overflow-hidden rounded border border-base-300 p-2"
        style={{
          backgroundColor: backgroundColor,
          color: textColor,
        }}
      >
        <p className="mb-2 whitespace-pre-line text-xs">
          {_(
            "All the world's a stage,\nAnd all the men and women merely players;\nThey have their exits and their entrances,\nAnd one man in his time plays many parts,\nHis acts being seven ages.\n\n— William Shakespeare",
          )}
          {"\n\n"}
          <span
            className="mt-4 cursor-pointer italic"
            style={{
              color: primaryColor,
            }}
          >
            {_("(from 'As You Like It', Act II)")}
          </span>
        </p>
      </div>
    </div>
  );

  const getCustomTheme = () => {
    return {
      name: md5Fingerprint(themeName),
      label: themeName,
      colors: {
        light: {
          fg: lightTextColor,
          bg: lightBackgroundColor,
          primary: lightPrimaryColor,
        },
        dark: {
          fg: darkTextColor,
          bg: darkBackgroundColor,
          primary: darkPrimaryColor,
        },
      },
    };
  };

  return (
    <div className="mt-6 rounded-lg">
      <div className="mb-4">
        <div className="mb-4 flex items-center justify-between">
          <label className="font-medium">{_("Custom Theme")}</label>
          <div className="flex w-[calc(50%-12px)] justify-between">
            <button className="btn btn-ghost btn-sm px-2 text-base-content" onClick={() => onSave(getCustomTheme())}>
              {_("Save")}
            </button>

            {settings.globalReadSettings.customThemes.find((theme) => theme.name === md5Fingerprint(themeName)) && (
              <button className={clsx("btn btn-ghost btn-sm px-2")} onClick={() => onDelete(getCustomTheme())}>
                {_("Delete")}
              </button>
            )}

            <button className="btn btn-ghost btn-sm px-2" onClick={onCancel}>
              {_("Cancel")}
            </button>
          </div>
        </div>
        <div className="mb-4 flex items-center justify-between">
          <label className="font-medium">{_("Theme Name")}</label>
          <input
            type="text"
            value={themeName}
            onChange={(e) => setThemeName(e.target.value)}
            className="w-[calc(50%-12px)] rounded border border-base-200 bg-base-100 p-2 text-base-content text-sm"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div className="rounded-lg bg-base-200 p-3">
          <h3 className="mb-3 text-center font-medium">{_("Light Mode")}</h3>

          <ColorInput label={_("Text Color")} value={lightTextColor} onChange={setLightTextColor} />

          <ColorInput label={_("Background Color")} value={lightBackgroundColor} onChange={setLightBackgroundColor} />

          <ColorInput label={_("Link Color")} value={lightPrimaryColor} onChange={setLightPrimaryColor} />

          <ThemePreview
            textColor={lightTextColor}
            backgroundColor={lightBackgroundColor}
            primaryColor={lightPrimaryColor}
            label={_("Preview")}
          />
        </div>

        <div className="rounded-lg bg-base-300 p-3">
          <h3 className="mb-3 text-center font-medium">{_("Dark Mode")}</h3>

          <ColorInput label={_("Text Color")} value={darkTextColor} onChange={setDarkTextColor} />

          <ColorInput label={_("Background Color")} value={darkBackgroundColor} onChange={setDarkBackgroundColor} />

          <ColorInput label={_("Link Color")} value={darkPrimaryColor} onChange={setDarkPrimaryColor} />

          <ThemePreview
            textColor={darkTextColor}
            backgroundColor={darkBackgroundColor}
            primaryColor={darkPrimaryColor}
            label={_("Preview")}
          />
        </div>
      </div>
    </div>
  );
};

export default ThemeEditor;
