import type React from "react";
import { TbLayoutSidebar, TbLayoutSidebarFilled } from "react-icons/tb";

import Button from "@/components/Button";
import { useReaderStore } from "@/store/readerStore";
import { useSidebarStore } from "@/store/sidebarStore";

interface SidebarTogglerProps {
  bookKey: string;
}

const SidebarToggler: React.FC<SidebarTogglerProps> = ({ bookKey }) => {
  const { sideBarBookKey, isSideBarVisible, setSideBarBookKey, toggleSideBar } = useSidebarStore();
  const { setHoveredBookKey } = useReaderStore();
  const handleToggleSidebar = () => {
    if (sideBarBookKey === bookKey) {
      toggleSideBar();
    } else {
      setSideBarBookKey(bookKey);
      if (!isSideBarVisible) toggleSideBar();
    }
    setHoveredBookKey("");
  };
  return (
    <Button
      icon={
        sideBarBookKey === bookKey && isSideBarVisible ? (
          <TbLayoutSidebarFilled size={20} className="text-base-content" />
        ) : (
          <TbLayoutSidebar size={20} className="text-base-content" />
        )
      }
      className="rounded-full"
      onClick={handleToggleSidebar}
    />
  );
};

export default SidebarToggler;
