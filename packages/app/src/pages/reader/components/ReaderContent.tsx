import type * as React from "react";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useSearchParams } from "react-router";

import { useEnv } from "@/context/EnvContext";
import { BOOK_IDS_SEPARATOR } from "@/services/constants";
import { useBookDataStore } from "@/store/bookDataStore";
import { useBookSettingsStore } from "@/store/bookSettingsStore";
import { useReaderStore } from "@/store/readerStore";
import { useSidebarStore } from "@/store/sidebarStore";
import type { Book } from "@/types/book";
import type { SystemSettings } from "@/types/settings";
import { eventDispatcher } from "@/utils/event";
import { navigateToLibrary } from "@/utils/nav";
import { throttle } from "@/utils/throttle";

import Spinner from "@/components/Spinner";
import useBookShortcuts from "../hooks/useBookShortcuts";
import useBooksManager from "../hooks/useBooksManager";
import BooksGrid from "./BooksGrid";

const ReaderContent: React.FC<{ ids?: string; settings: SystemSettings }> = ({ ids, settings }) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { envConfig } = useEnv();
  const { bookKeys, dismissBook, getNextBookKey } = useBooksManager();
  const { sideBarBookKey, setSideBarBookKey, setSidebarExpanded } = useSidebarStore();
  const { saveSettings } = useBookSettingsStore();
  const { getConfig, getBookData, saveConfig } = useBookDataStore();
  const { getView, setBookKeys, getViewSettings } = useReaderStore();
  const { initViewState, getViewState, clearViewState } = useReaderStore();
  const isInitiating = useRef(false);
  const [loading, setLoading] = useState(false);

  useBookShortcuts({ sideBarBookKey, bookKeys });

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isInitiating.current) return;
    isInitiating.current = true;

    const bookIds = ids || searchParams?.get("ids") || "";
    const initialIds = bookIds.split(BOOK_IDS_SEPARATOR).filter(Boolean);
    const initialBookKeys = initialIds.map((id) => `${id}`);
    setBookKeys(initialBookKeys);
    const uniqueIds = new Set<string>();
    console.log("Initialize books", initialBookKeys);
    initialBookKeys.forEach((key, index) => {
      const id = key.split("-")[0]!;
      const isPrimary = !uniqueIds.has(id);
      uniqueIds.add(id);
      if (!getViewState(key)) {
        initViewState(envConfig, id, key, isPrimary).catch((error: any) => {
          console.log("Error initializing book", key, error);
        });
        if (index === 0) setSideBarBookKey(key);
      }
    });

    const handleShowBookDetails = (event: CustomEvent) => {
      const book = event.detail as Book;
      return true;
    };
    eventDispatcher.onSync("show-book-details", handleShowBookDetails);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (bookKeys && bookKeys.length > 0) {
      const settings = useBookSettingsStore.getState().settings;
      settings.lastOpenBooks = bookKeys.map((key: string) => key.split("-")[0]!);
      saveSettings(envConfig, settings);
    }

    window.addEventListener("beforeunload", handleCloseBooks);
    eventDispatcher.on("beforereload", handleCloseBooks);
    eventDispatcher.on("quit-app", handleCloseBooks);
    return () => {
      window.removeEventListener("beforeunload", handleCloseBooks);
      eventDispatcher.off("beforereload", handleCloseBooks);
      eventDispatcher.off("quit-app", handleCloseBooks);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bookKeys]);

  const saveBookConfig = async (bookKey: string) => {
    const config = getConfig(bookKey);
    const { book } = getBookData(bookKey) || {};
    const { isPrimary } = getViewState(bookKey) || {};
    if (isPrimary && book && config) {
      eventDispatcher.dispatch("sync-book-progress", { bookKey });
      const settings = useBookSettingsStore.getState().settings;
      await saveConfig(envConfig, bookKey, config, settings);
    }
  };

  const saveConfigAndCloseBook = async (bookKey: string) => {
    console.log("Closing book", bookKey);
    try {
      getView(bookKey)?.close();
      getView(bookKey)?.remove();
    } catch {
      console.info("Error closing book", bookKey);
    }
    eventDispatcher.dispatch("tts-stop", { bookKey });
    await saveBookConfig(bookKey);
    clearViewState(bookKey);
  };

  const saveSettingsAndGoToLibrary = () => {
    saveSettings(envConfig, settings);
    navigateToLibrary(navigate);
  };

  const handleCloseBooks = throttle(async () => {
    const settings = useBookSettingsStore.getState().settings;
    await Promise.all(bookKeys.map((key: string) => saveConfigAndCloseBook(key)));
    await saveSettings(envConfig, settings);
  }, 200);

  const handleCloseBooksToLibrary = () => {
    handleCloseBooks();
    setSidebarExpanded(true);
    navigateToLibrary(navigate);
  };

  const handleCloseBook = async (bookKey: string) => {
    saveConfigAndCloseBook(bookKey);
    if (sideBarBookKey === bookKey) {
      setSideBarBookKey(getNextBookKey(sideBarBookKey));
    }
    dismissBook(bookKey);
    if (bookKeys.filter((key: string) => key !== bookKey).length === 0) {
      saveSettingsAndGoToLibrary();
    }
  };

  if (!bookKeys || bookKeys.length === 0) return null;
  const bookData = getBookData(bookKeys[0]!);
  const viewSettings = getViewSettings(bookKeys[0]!);
  if (!bookData || !bookData.book || !bookData.bookDoc || !viewSettings) {
    setTimeout(() => setLoading(true), 300);
    return (
      loading && (
        <div className="reader-content flex h-dvh">
          <Spinner loading={true} />
        </div>
      )
    );
  }

  return (
    <div className="reader-content flex h-[calc(100vh-50px)] rounded-md">
      <BooksGrid bookKeys={bookKeys} onCloseBook={handleCloseBook} onGoToLibrary={handleCloseBooksToLibrary} />
    </div>
  );
};

export default ReaderContent;
