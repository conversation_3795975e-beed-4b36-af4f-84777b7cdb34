import clsx from "clsx";
import type React from "react";
import { useEffect, useState } from "react";

import { useTranslation } from "@/hooks/useTranslation";
import { useBookDataStore } from "@/store/bookDataStore";
import { useReaderStore } from "@/store/readerStore";
import type { PageInfo } from "@/types/book";
import type { Insets } from "@/types/misc";
import { viewPagination } from "../hooks/usePagination";

interface FooterBarProps {
  bookKey: string;
  bookFormat: string;
  section?: PageInfo;
  pageinfo?: PageInfo;
  isHoveredAnim: boolean;
  gridInsets: Insets;
}

const FooterBar: React.FC<FooterBarProps> = ({ bookKey, bookFormat, section, pageinfo }) => {
  const [showControls, setShowControls] = useState(true);
  const _ = useTranslation();
  const { getView, getViewSettings } = useReaderStore();
  const { getBookData } = useBookDataStore();

  const view = getView(bookKey);
  const viewSettings = getViewSettings(bookKey);

  useEffect(() => {
    const handleIframeMouseMove = (event: MessageEvent) => {
      if (event.data && event.data.type === "iframe-mousemove") {
        const bookData = getBookData(bookKey);
        if (bookData && event.data.bookKey === bookKey) {
          const { clientX } = event.data;
          const container = document.getElementById(`gridcell-${bookKey}`);
          if (container) {
            const containerRect = container.getBoundingClientRect();
            const containerWidth = containerRect.width;
            const leftBoundary = containerWidth * 0.02;
            const rightBoundary = containerWidth * 0.92;
            if (clientX <= leftBoundary || clientX >= rightBoundary) {
              setShowControls(true);
            }
          }
        }
      }
    };

    window.addEventListener("message", handleIframeMouseMove);
    return () => window.removeEventListener("message", handleIframeMouseMove);
  }, [bookKey, getBookData]);

  const handleGoPrevPage = () => {
    const isScrolledMode = viewSettings?.scrolled;
    if (isScrolledMode) {
      view?.renderer.prevSection?.();
    } else {
      viewPagination(view, viewSettings, "left");
    }
  };

  const handleGoNextPage = () => {
    const isScrolledMode = viewSettings?.scrolled;
    if (isScrolledMode) {
      view?.renderer.nextSection?.();
    } else {
      viewPagination(view, viewSettings, "right");
    }
  };

  const isVertical = viewSettings?.vertical;
  const isScrolledMode = viewSettings?.scrolled;

  const pageInfo = ["PDF", "CBZ"].includes(bookFormat)
    ? section
      ? isVertical
        ? `${section.current + 1} · ${section.total}`
        : `${section.current + 1} / ${section.total}`
      : ""
    : pageinfo && pageinfo.current >= 0 && pageinfo.total > 0
      ? _(isVertical ? "{{currentPage}} · {{totalPage}}" : "Loc. {{currentPage}} / {{totalPage}}", {
          currentPage: pageinfo.current + 1,
          totalPage: pageinfo.total,
        })
      : "";

  return (
    <>
      <button
        className={clsx(
          "-translate-y-1/2 absolute top-1/2 left-1 z-50",
          "flex cursor-pointer items-center justify-center",
          "rounded-md hover:bg-neutral-200 hover:text-neutral-600 dark:hover:bg-neutral-600 dark:hover:text-neutral-400",
          "h-12 w-6 text-neutral-300 transition-all duration-300 dark:text-neutral-500",
          "hover:scale-105",
          showControls ? "translate-x-0 opacity-100" : "-translate-x-4 pointer-events-none opacity-0",
        )}
        onClick={handleGoPrevPage}
        title={isScrolledMode ? _("Previous Chapter") : _("Previous Page")}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="3.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="m14 21-5-9 5-9" />
        </svg>
      </button>

      <div className="-translate-x-1/2 absolute bottom-4 left-1/2 z-50">
        <span className="text-center text-sm">{pageInfo}</span>
      </div>

      <button
        className={clsx(
          "-translate-y-1/2 absolute top-1/2 right-1 z-50",
          "flex cursor-pointer items-center justify-center",
          "rounded-md hover:bg-neutral-200 hover:text-neutral-600 dark:hover:bg-neutral-600 dark:hover:text-neutral-400",
          "h-12 w-6 text-neutral-300 transition-all duration-300 dark:text-neutral-500",
          "hover:scale-105",
          showControls ? "translate-x-0 opacity-100" : "pointer-events-none translate-x-4 opacity-0",
        )}
        onClick={handleGoNextPage}
        title={isScrolledMode ? _("Next Chapter") : _("Next Page")}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="3.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="m10 21 5-9-5-9" />
        </svg>
      </button>
    </>
  );
};

export default FooterBar;
