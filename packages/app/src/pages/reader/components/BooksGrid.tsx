import clsx from "clsx";
import type React from "react";
import { useEffect, useState } from "react";

import { useEnv } from "@/context/EnvContext";
import { useSafeAreaInsets } from "@/hooks/useSafeAreaInsets";
import { useBookDataStore } from "@/store/bookDataStore";
import { useBookSettingsStore } from "@/store/bookSettingsStore";
import { useReaderStore } from "@/store/readerStore";
import { useSidebarStore } from "@/store/sidebarStore";
import { getGridTemplate, getInsetEdges } from "@/utils/grid";
import { getViewInsets } from "@/utils/insets";
import DoubleBorder from "./DoubleBorder";
import FoliateViewer from "./FoliateViewer";
import FooterBar from "./FooterBar";
import FootnotePopup from "./FootnotePopup";
import HeaderBar from "./HeaderBar";
import HintInfo from "./HintInfo";
import Ribbon from "./Ribbon";
import Annotator from "./annotator/Annotator";

interface BooksGridProps {
  bookKeys: string[];
  onCloseBook: (bookKey: string) => void;
  onGoToLibrary: () => void;
}

const BooksGrid: React.FC<BooksGridProps> = ({ bookKeys, onCloseBook, onGoToLibrary }) => {
  const { appService } = useEnv();
  const { getConfig, getBookData } = useBookDataStore();
  const { getProgress, getViewState, getViewSettings } = useReaderStore();
  const { setGridInsets, hoveredBookKey } = useReaderStore();
  const { sideBarBookKey } = useSidebarStore();
  const { setFontLayoutSettingsDialogOpen } = useBookSettingsStore();

  // 延迟渲染 FoliateViewer
  const [shouldRenderViewers, setShouldRenderViewers] = useState(false);

  const screenInsets = useSafeAreaInsets();
  const aspectRatio = window.innerWidth / window.innerHeight;
  const gridTemplate = getGridTemplate(bookKeys.length, aspectRatio);

  // 延迟渲染 FoliateViewer，避免阻塞动画
  useEffect(() => {
    const timer = setTimeout(() => {
      setShouldRenderViewers(true);
    }, 50); // 延迟 50ms 渲染

    return () => clearTimeout(timer);
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (!sideBarBookKey) return;
    const bookData = getBookData(sideBarBookKey);
    if (!bookData || !bookData.book) return;
    document.title = bookData.book.title;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sideBarBookKey]);

  const calcGridInsets = (index: number, count: number) => {
    if (!screenInsets) return { top: 0, right: 0, bottom: 0, left: 0 };
    const { top, right, bottom, left } = getInsetEdges(index, count, aspectRatio);
    return {
      top: top ? screenInsets.top : 0,
      right: right ? screenInsets.right : 0,
      bottom: bottom ? screenInsets.bottom : 0,
      left: left ? screenInsets.left : 0,
    };
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (!screenInsets) return;
    bookKeys.forEach((bookKey, index) => {
      const gridInsets = calcGridInsets(index, bookKeys.length);
      setGridInsets(bookKey, gridInsets);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bookKeys, screenInsets]);

  if (!screenInsets) return null;

  return (
    <div
      className={clsx("books-grid relative grid h-full flex-grow")}
      style={{
        gridTemplateColumns: gridTemplate.columns,
        gridTemplateRows: gridTemplate.rows,
      }}
    >
      {bookKeys.map((bookKey, index) => {
        const bookData = getBookData(bookKey);
        const config = getConfig(bookKey);
        const progress = getProgress(bookKey);
        const viewSettings = getViewSettings(bookKey);
        const gridInsets = calcGridInsets(index, bookKeys.length);
        const { book, bookDoc } = bookData || {};
        if (!book || !config || !bookDoc || !viewSettings) return null;

        const { section, pageinfo, sectionLabel } = progress || {};
        const isBookmarked = getViewState(bookKey)?.ribbonVisible;
        const horizontalGapPercent = viewSettings.gapPercent;
        const viewInsets = getViewInsets(viewSettings);
        const contentInsets = {
          top: gridInsets.top + viewInsets.top,
          right: gridInsets.right + viewInsets.right,
          bottom: gridInsets.bottom + viewInsets.bottom,
          left: gridInsets.left + viewInsets.left,
        };
        const scrolled = viewSettings.scrolled;
        const showBarsOnScroll = viewSettings.showBarsOnScroll;
        const showHeader = viewSettings.showHeader && (scrolled ? showBarsOnScroll : true);
        const showFooter = viewSettings.showFooter && (scrolled ? showBarsOnScroll : true);

        return (
          <div
            id={`gridcell-${bookKey}`}
            key={bookKey}
            className={clsx(
              "relative h-full w-full overflow-hidden bg-background",
              appService?.hasRoundedWindow && "rounded-window",
            )}
          >
            {isBookmarked && !hoveredBookKey && <Ribbon width={`${horizontalGapPercent}%`} />}
            <HeaderBar
              bookKey={bookKey}
              bookTitle={book.title}
              isTopLeft={index === 0}
              isHoveredAnim={bookKeys.length > 2}
              onCloseBook={onCloseBook}
              onSetSettingsDialogOpen={setFontLayoutSettingsDialogOpen}
              gridInsets={gridInsets}
              onGoToLibrary={onGoToLibrary}
              section={sectionLabel || ""}
            />
            <div className="relative m-2 mt-0 flex h-[calc(100%-52px)] flex-col">
              {shouldRenderViewers ? (
                <FoliateViewer bookKey={bookKey} bookDoc={bookDoc} config={config} contentInsets={contentInsets} />
              ) : (
                <div className="flex h-full w-full items-center justify-center rounded-md">
                  <div className="absolute inset-0 z-50 flex items-center justify-center bg-white/70 backdrop-blur-sm dark:bg-neutral-900/60" />
                </div>
              )}
              <FooterBar
                bookKey={bookKey}
                bookFormat={book.format}
                section={section}
                pageinfo={pageinfo}
                isHoveredAnim={false}
                gridInsets={gridInsets}
              />
            </div>
            {viewSettings.vertical && viewSettings.scrolled && (
              <>
                {(showFooter || viewSettings.doubleBorder) && (
                  <div
                    className="absolute top-0 left-0 h-full bg-base-100"
                    style={{
                      width: `calc(${contentInsets.left + (showFooter ? 32 : 0)}px)`,
                      height: "calc(100%)",
                    }}
                  />
                )}
                {(showHeader || viewSettings.doubleBorder) && (
                  <div
                    className="absolute top-0 right-0 h-full bg-base-100"
                    style={{
                      width: `calc(${contentInsets.right + (showHeader ? 32 : 0)}px)`,
                      height: "calc(100%)",
                    }}
                  />
                )}
              </>
            )}
            {viewSettings.vertical && viewSettings.doubleBorder && (
              <DoubleBorder
                showHeader={showHeader}
                showFooter={showFooter}
                borderColor={viewSettings.borderColor}
                horizontalGap={horizontalGapPercent}
                contentInsets={contentInsets}
              />
            )}
            {/* {showHeader && (
              <SectionInfo
                bookKey={bookKey}
                section={sectionLabel}
                showDoubleBorder={viewSettings.vertical && viewSettings.doubleBorder}
                isScrolled={viewSettings.scrolled}
                isVertical={viewSettings.vertical}
                horizontalGap={horizontalGapPercent}
                contentInsets={contentInsets}
                gridInsets={gridInsets}
              />
            )} */}
            <HintInfo
              bookKey={bookKey}
              showDoubleBorder={viewSettings.vertical && viewSettings.doubleBorder}
              isScrolled={viewSettings.scrolled}
              isVertical={viewSettings.vertical}
              horizontalGap={horizontalGapPercent}
              contentInsets={contentInsets}
              gridInsets={gridInsets}
            />
            {/* {showFooter && (
              <ProgressInfoView
                bookKey={bookKey}
                bookFormat={book.format}
                section={section}
                pageinfo={pageinfo}
                timeinfo={timeinfo}
                horizontalGap={horizontalGapPercent}
                contentInsets={contentInsets}
                gridInsets={gridInsets}
              />
            )} */}
            <Annotator bookKey={bookKey} />
            <FootnotePopup bookKey={bookKey} bookDoc={bookDoc} />
          </div>
        );
      })}
    </div>
  );
};

export default BooksGrid;
