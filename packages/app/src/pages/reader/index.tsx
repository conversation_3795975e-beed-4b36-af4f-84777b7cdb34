import ChatContent from "@/components/chat/chat-sidebar";
import { Resizable } from "re-resizable";
import { useParams } from "react-router";
import Reader from "./components/Reader";

// This is only used for the Tauri app in the app router
export default function Page() {
  const { ids } = useParams();
  return (
    <div className="flex h-screen">
      <div className="flex-1">
        <Reader ids={ids} />
      </div>
      <Resizable
        defaultSize={{
          width: 400,
          height: "100%",
        }}
        minWidth={300}
        maxWidth={600}
        enable={{
          top: false,
          right: false,
          bottom: false,
          left: true,
          topRight: false,
          bottomRight: false,
          bottomLeft: false,
          topLeft: false,
        }}
        handleComponent={{
          left: <div className="custom-resize-handle" />,
        }}
      >
        <div className="h-full">
          <ChatContent />
        </div>
      </Resizable>
    </div>
  );
}
