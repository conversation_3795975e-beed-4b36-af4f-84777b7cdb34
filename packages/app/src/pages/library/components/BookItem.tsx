import { useTabsContext } from "@/context/TabsContext";
import { useDownloadImage } from "@/hooks/useDownloadImage";
import { useModelSelector } from "@/hooks/useModelSelector";
import { useTranslation } from "@/hooks/useTranslation";
import type { BookTag } from "@/pages/library/hooks/useTagsManagement";
import type { BookWithStatusAndUrls } from "@/types/simpleBook";

import AITagConfirmDialog from "@/components/AITagConfirmDialog";
import { type AITagSuggestion, generateTagsWithAI } from "@/services/aiTagService";
import { updateBookVectorizationMeta } from "@/services/bookService";
import { type EpubIndexResult, type TocNode, convertBookToMdbook, indexEpub, parseToc } from "@/services/bookService";
import { createTag, getTags } from "@/services/tagService";
import { useLlamaStore } from "@/store/llamaStore";
// import { invoke } from "@tauri-apps/api/core";
import { listen } from "@tauri-apps/api/event";
import { Menu, MenuItem, PredefinedMenuItem, Submenu } from "@tauri-apps/api/menu";
import { LogicalPosition } from "@tauri-apps/api/window";
import { ask } from "@tauri-apps/plugin-dialog";
import { MoreHorizontal } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import EditInfo from "./EditInfo";
import EmbeddingDialog from "./EmbeddingDialog";

interface BookUpdateData {
  title?: string;
  author?: string;
  coverPath?: string;
  tags?: string[];
}

interface BookItemProps {
  book: BookWithStatusAndUrls;
  viewMode?: "grid" | "list";
  availableTags?: BookTag[];
  onDelete?: (book: BookWithStatusAndUrls) => Promise<boolean>;
  onUpdate?: (bookId: string, updates: BookUpdateData) => Promise<boolean>;
  onRefresh?: () => Promise<void>;
}

export default function BookItem({ book, availableTags = [], onDelete, onUpdate, onRefresh }: BookItemProps) {
  const { currentSession } = useLlamaStore();
  const _ = useTranslation();
  const [showEditDialog, setShowEditDialog] = useState(false);
  const { downloadImage } = useDownloadImage();
  const { openReader } = useTabsContext();

  // AI标签生成相关状态
  const [showAITagDialog, setShowAITagDialog] = useState(false);
  const [aiTagSuggestions, setAiTagSuggestions] = useState<AITagSuggestion[]>([]);
  const [isAITagLoading, setIsAITagLoading] = useState(false);
  const { selectedModel } = useModelSelector();
  const [showEmbeddingDialog, setShowEmbeddingDialog] = useState(false);
  const [vectorizeProgress, setVectorizeProgress] = useState<number | null>(null);

  useEffect(() => {
    let unlisten: (() => void) | null = null;
    (async () => {
      const off = await listen<{
        book_id: string;
        current: number;
        total: number;
        percent: number;
        chapter_title: string;
        chunk_index: number;
      }>("epub://index-progress", (e) => {
        const p = e.payload;
        if (p && p.book_id === book.id) {
          setVectorizeProgress(Math.max(0, Math.min(100, Math.round(p.percent))));
        }
      });
      unlisten = off;
    })();
    return () => {
      if (unlisten) unlisten();
    };
  }, [book.id]);

  const handleClick = useCallback(() => {
    openReader([book.id], book.title);
  }, [openReader, book.id, book.title]);

  // AI生成标签处理函数
  const handleAIGenerateTags = useCallback(async () => {
    if (!selectedModel) {
      toast.error("请先在设置中配置AI模型");
      return;
    }

    setIsAITagLoading(true);

    // 显示正在请求的toast
    toast.info("正在请求AI生成标签...");

    try {
      // 获取现有标签
      const existingTags = await getTags();

      // 调用AI生成标签
      const aiResponse = await generateTagsWithAI(book, existingTags, {
        providerId: selectedModel.providerId,
        modelId: selectedModel.modelId,
      });

      setAiTagSuggestions(aiResponse.suggestions);
      setShowAITagDialog(true);
    } catch (error) {
      console.error("AI生成标签失败:", error);
      toast.error(error instanceof Error ? error.message : "AI生成标签失败，请重试");
    } finally {
      setIsAITagLoading(false);
    }
  }, [selectedModel, book]);

  const handleAITagConfirm = useCallback(
    async (selectedTags: { name: string; isExisting: boolean; existingTagId?: string }[]) => {
      if (selectedTags.length === 0) {
        setShowAITagDialog(false);
        return;
      }

      setIsAITagLoading(true);

      try {
        const tagIds: string[] = [];

        for (const tag of selectedTags) {
          if (tag.isExisting && tag.existingTagId) {
            tagIds.push(tag.existingTagId);
          } else {
            const newTag = await createTag({
              name: tag.name,
              color: `#${Math.floor(Math.random() * 16777215).toString(16)}`,
            });
            tagIds.push(newTag.id);
          }
        }

        const currentTags = book.tags || [];
        const updatedTags = Array.from(new Set([...currentTags, ...tagIds]));

        if (onUpdate) {
          const success = await onUpdate(book.id, { tags: updatedTags });

          if (success) {
            toast.success(`成功添加 ${selectedTags.length} 个标签`);

            if (onRefresh) {
              await onRefresh();
            }
          } else {
            toast.error("添加标签失败，请重试");
          }
        }

        setShowAITagDialog(false);
      } catch (error) {
        console.error("添加AI标签失败:", error);
        toast.error(error instanceof Error ? error.message : "添加标签失败，请重试");
      } finally {
        setIsAITagLoading(false);
      }
    },
    [book, onUpdate, onRefresh],
  );

  const handleNativeDelete = useCallback(async () => {
    if (onDelete) {
      try {
        const confirmed = await ask(`${book.title}\n\n${_("This action cannot be undone.")}`, {
          title: _("Confirm Deletion"),
          kind: "warning",
        });

        if (confirmed) {
          await onDelete(book);
        }
      } catch (error) {
        console.error("Failed to show delete dialog:", error);
      }
    }
  }, [_, onDelete, book]);

  const handleDownloadImage = useCallback(async () => {
    if (!book.coverUrl) {
      console.warn("No cover image available for download");
      return;
    }

    await downloadImage(book.coverUrl, {
      title: book.title,
      defaultFileName: `${book.title}_cover`,
    });
  }, [book.coverUrl, book.title, downloadImage]);

  // Extracted vectorization action
  const handleVectorizeBook = useCallback(async () => {
    // 获取向量模型配置（优先使用外部配置，回退到本地服务）
    const llamaState = (await import("@/store/llamaStore")).useLlamaStore.getState();

    let baseUrl: string;
    let model: string;
    let apiKey: string | null = null;

    // 如果启用了向量模型功能且有选中的模型，优先使用
    if (llamaState.vectorModelEnabled) {
      const selectedModel = llamaState.getSelectedVectorModel();
      if (selectedModel) {
        baseUrl = selectedModel.url.endsWith("/embeddings")
          ? selectedModel.url.replace("/embeddings", "")
          : selectedModel.url.replace(/\/$/, "");
        model = selectedModel.modelId;
        apiKey = selectedModel.apiKey || null;
      } else {
        // 启用了向量模型但没有选中的模型，回退到本地服务
        const port = llamaState.currentSession?.port;
        baseUrl = port ? `http://127.0.0.1:${port}` : "http://127.0.0.1:3544";
        model = "local-embed";
      }
    } else {
      // 回退到本地 llama.cpp 服务
      const port = llamaState.currentSession?.port;
      baseUrl = port ? `http://127.0.0.1:${port}` : "http://127.0.0.1:3544";
      model = "local-embed";
    }

    const dimension = 1024;
    const version = 1;

    try {
      toast.info("开始向量化...");
      setVectorizeProgress(0); // 开始时立即显示 0%
      await updateBookVectorizationMeta(book.id, {
        status: "processing",
        model,
        dimension,
        version,
        startedAt: Date.now(),
      });

      const res: EpubIndexResult = await indexEpub(book.id, {
        dimension,
        baseUrl,
        model,
        apiKey,
      });

      if (res?.success && res.report) {
        await updateBookVectorizationMeta(book.id, {
          status: "success",
          chunkCount: res.report.total_chunks,
          dimension: res.report.vector_dimension,
          finishedAt: Date.now(),
        });
      } else {
        await updateBookVectorizationMeta(book.id, {
          status: "failed",
          finishedAt: Date.now(),
        });
        throw new Error(res?.message || "向量化失败");
      }
      toast.success(`向量化完成，分块数：${res.report?.total_chunks ?? "未知"}`);
      setVectorizeProgress(null); // 结束后由元数据状态驱动显示（绿环）
      if (onRefresh) await onRefresh();
    } catch (err) {
      console.error("向量化失败", err);
      await updateBookVectorizationMeta(book.id, {
        status: "failed",
        finishedAt: Date.now(),
      });
      setVectorizeProgress(null); // 失败也结束进度显示
      toast.error("向量化失败，请检查嵌入服务是否可用");
      if (onRefresh) await onRefresh();
    }
  }, [book.id, onRefresh]);

  const handleTagToggle = useCallback(
    async (tagId: string) => {
      if (!onUpdate) return;

      const currentTags = book.tags || [];
      const hasTag = currentTags.includes(tagId);

      let newTags: string[];
      if (hasTag) {
        // 移除标签
        newTags = currentTags.filter((tag) => tag !== tagId);
      } else {
        // 添加标签（去重）
        newTags = Array.from(new Set([...currentTags, tagId]));
      }

      try {
        await onUpdate(book.id, { tags: newTags });
      } catch (error) {
        console.error("Failed to update tags:", error);
      }
    },
    [book.id, book.tags, onUpdate],
  );

  const renderProgress = () => {
    if (!book.status || book.status.status === "unread") {
      return (
        <div className="inline-block rounded-full bg-neutral-100 px-2 py-1 font-medium text-neutral-600 text-xs dark:bg-neutral-800 dark:text-neutral-300">
          New
        </div>
      );
    }

    const progress =
      book.status.progressTotal > 0 ? Math.round((book.status.progressCurrent / book.status.progressTotal) * 100) : 0;

    return <div className="text-neutral-500 text-xs dark:text-neutral-400">{progress}%</div>;
  };

  const renderVectorizationStatus = () => {
    const statusFromMeta = book.status?.metadata?.vectorization?.status ?? "idle";
    const effectiveStatus =
      vectorizeProgress != null && vectorizeProgress >= 0 && vectorizeProgress < 100 ? "processing" : statusFromMeta;

    if (effectiveStatus === "processing") {
      const pct = Math.max(0, Math.min(100, vectorizeProgress ?? 0));
      return (
        <div className="flex items-center gap-1" title={`向量化: processing ${pct}%`}>
          <div className="relative h-4 w-4" aria-label={`processing ${pct}%`}>
            <div
              className="absolute inset-0 rounded-full"
              style={{ background: `conic-gradient(#eab308 ${pct}%, rgba(229,231,235,0.6) 0)` }}
            />
            <div className="absolute inset-[2px] rounded-full bg-white dark:bg-neutral-900" />
          </div>
          <span className="text-[10px] text-neutral-500 leading-none dark:text-neutral-400">{pct}%</span>
        </div>
      );
    }

    const colorClass =
      effectiveStatus === "success"
        ? "border-green-500"
        : effectiveStatus === "failed"
          ? "border-red-500"
          : "border-neutral-400 dark:border-neutral-500";
    return (
      <div className="flex items-center gap-1" title={`向量化: ${effectiveStatus}`}>
        <div className={`h-3.5 w-3.5 rounded-full border-2 ${colorClass}`} />
      </div>
    );
  };

  const handleMenuClick = useCallback(
    async (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      try {
        const separator1 = await PredefinedMenuItem.new({ text: "separator-1", item: "Separator" });
        const separator2 = await PredefinedMenuItem.new({ text: "separator-2", item: "Separator" });
        const separator3 = await PredefinedMenuItem.new({ text: "separator-3", item: "Separator" });

        const isUnread = !book.status || book.status.status === "unread";
        const markStatusItem = {
          id: isUnread ? "mark-read" : "mark-unread",
          text: isUnread ? _("Mark as Read") : _("Mark as Unread"),
          action: () => {
            if (isUnread) {
              console.log("Mark as Read clicked");
            } else {
              console.log("Mark as Unread clicked");
            }
          },
        };

        const currentTags = book.tags || [];

        const allTagMenuItems: any[] = [];

        const aiGenerateItem = await MenuItem.new({
          id: "ai-generate-tags",
          text: "AI 生成",
          action: () => {
            handleAIGenerateTags();
          },
        });
        allTagMenuItems.push(aiGenerateItem);

        if (availableTags.length > 0) {
          const aiSeparator = await PredefinedMenuItem.new({ text: "ai-separator", item: "Separator" });
          allTagMenuItems.push(aiSeparator);
          const databaseTags = await getTags();
          const tagMenuItems = await Promise.all(
            availableTags
              .filter((tag) => tag.id !== "all" && tag.id !== "uncategorized")
              .map(async (tag) => {
                const tagName = tag.id.startsWith("tag-") ? tag.id.replace("tag-", "") : tag.name;
                const dbTag = databaseTags.find((t) => t.name === tagName);
                const realTagId = dbTag?.id;
                const hasTag = realTagId ? currentTags.includes(realTagId) : false;

                return await MenuItem.new({
                  id: `tag-${tag.id}`,
                  text: `${hasTag ? "✓ " : ""}${tagName}`,
                  action: () => {
                    if (realTagId) {
                      handleTagToggle(realTagId);
                    }
                  },
                });
              }),
          );

          allTagMenuItems.push(...tagMenuItems);
        }

        const tagsSubmenu = await Submenu.new({
          text: "管理标签",
          items: allTagMenuItems,
        });

        const isVectorized = book.status?.metadata?.vectorization?.status === "success";
        const vectorizeItem = await MenuItem.new({
          id: "vectorize-epub",
          text: `${isVectorized ? "✓ " : ""}向量化`,
          action: async () => {
            await handleVectorizeBook();
          },
        });

        const testVectorItem = await MenuItem.new({
          id: "test-vector",
          text: "向量化测试",
          action: () => {
            setShowEmbeddingDialog(true);
          },
        });

        const mdItem = await MenuItem.new({
          id: "convert-mdbook",
          text: "转换为 Markdown",
          action: async () => {
            try {
              const { outputDir } = await convertBookToMdbook(book.id, true);
              toast.success(`已转换为 Markdown: ${outputDir}`);
            } catch (e) {
              console.error(e);
              toast.error(e instanceof Error ? e.message : "Markdown 转换失败");
            }
          },
        });

        const parseToC = await MenuItem.new({
          id: "parse-toc",
          text: "解析目录结构",
          action: async () => {
            try {
              const tocNodes = await parseToc(book.id);
              console.log("TOC 结构:", tocNodes);
              toast.success(`成功解析目录结构，共 ${tocNodes.length} 个顶级章节`);
            } catch (e) {
              console.error(e);
              toast.error(e instanceof Error ? e.message : "解析目录结构失败");
            }
          },
        });

        const menu = await Menu.new({
          items: [
            {
              id: "open",
              text: _("Open"),
              action: () => {
                handleClick();
              },
            },
            vectorizeItem,
            mdItem,
            parseToC,
            testVectorItem,
            separator1,
            {
              id: "edit",
              text: _("Edit Info"),
              action: () => {
                setShowEditDialog(true);
              },
            },
            ...(book.coverUrl
              ? [
                  {
                    id: "download-image",
                    text: _("Download Image"),
                    action: () => {
                      handleDownloadImage();
                    },
                  },
                ]
              : []),
            tagsSubmenu,
            separator2,
            markStatusItem,
            separator3,
            {
              id: "delete",
              text: _("Delete"),
              action: () => {
                handleNativeDelete();
              },
            },
          ],
        });

        await menu.popup(new LogicalPosition(e.clientX, e.clientY));
      } catch (error) {
        console.error("Failed to show native menu:", error);
      }
    },
    [
      _,
      handleClick,
      handleNativeDelete,
      handleDownloadImage,
      handleTagToggle,
      handleAIGenerateTags,
      handleVectorizeBook,
      book.coverUrl,
      book.status,
      availableTags,
      book.tags,
      book.id,
    ],
  );

  return (
    <>
      <div className="group cursor-pointer" onClick={handleClick}>
        <div className="rounded-r-2xl rounded-l-md border border-neutral-200 bg-white shadow-sm transition-shadow hover:shadow-md dark:border-neutral-700 dark:bg-neutral-800">
          <div className="relative p-2 pb-0">
            <div className="mb-2">
              <h4 className="truncate text-neutral-600 text-sm leading-tight dark:text-neutral-200">{book.title}</h4>
            </div>

            <div className="aspect-[4/5] w-full overflow-hidden" onContextMenu={handleMenuClick}>
              {book.coverUrl ? (
                <img src={book.coverUrl} alt={book.title} className="h-full w-full object-cover" />
              ) : (
                <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-neutral-100 to-neutral-300 dark:from-neutral-700 dark:to-neutral-800">
                  <div className="p-4 text-center">
                    <div className="mb-2 font-bold text-2xl text-neutral-500 dark:text-neutral-400">📖</div>
                    <div className="line-clamp-3 text-neutral-600 text-xs dark:text-neutral-300">{book.title}</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between space-x-2 p-2 pt-0">
            <div className="flex-1">{renderProgress()}</div>
            <div className="flex items-center gap-2">
              {renderVectorizationStatus()}
              <MoreHorizontal onClick={handleMenuClick} className="h-4 w-4 text-neutral-500 dark:text-neutral-400" />
            </div>
          </div>
        </div>
      </div>

      <EditInfo book={book} isOpen={showEditDialog} onClose={() => setShowEditDialog(false)} onSave={onUpdate} />

      <AITagConfirmDialog
        isOpen={showAITagDialog}
        onClose={() => setShowAITagDialog(false)}
        suggestions={aiTagSuggestions}
        bookTitle={book.title}
        bookAuthor={book.author}
        onConfirm={handleAITagConfirm}
        isLoading={isAITagLoading}
      />

      <EmbeddingDialog isOpen={showEmbeddingDialog} onClose={() => setShowEmbeddingDialog(false)} bookId={book.id} />
    </>
  );
}
