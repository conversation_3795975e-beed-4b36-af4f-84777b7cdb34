import type { BookWithStatusAndUrls } from "@/types/simpleBook";
import clsx from "clsx";
import { ChevronDown, ChevronRight, Library, Notebook, Settings } from "lucide-react";
import { Link, useLocation } from "react-router";
import type { BookTag } from "../hooks/useTagsManagement";
import SearchToggle from "./SearchToggle";
import TagList from "./TagList";

interface NavigationItem {
  path: string;
  label: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
}

interface ActionButtonItem {
  label: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  onClick: () => void;
}

interface SidebarProps {
  searchQuery: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  tags: BookTag[];
  selectedTag: string;
  selectedTagsForDelete: string[];
  isLibraryExpanded: boolean;
  toggleLibraryExpanded: () => void;
  handleTagClick: (tagId: string, event: React.MouseEvent) => void;
  handleTagContextMenu: (e: React.MouseEvent, tag: BookTag) => void;
  handleNewTagClick: () => void;
  triggerFileSelect: () => void;
  toggleSettingsDialog: () => void;
  books: BookWithStatusAndUrls[];
  onBookUpdate: (bookId: string, updates: { tags?: string[] }) => Promise<boolean>;
  onRefresh: () => Promise<void>;
}

const navigationItems: NavigationItem[] = [
  {
    path: "/",
    label: "图书馆",
    icon: Library,
  },
  {
    path: "/note",
    label: "笔记",
    icon: Notebook,
  },
];

export default function Sidebar({
  searchQuery,
  onSearchChange,
  tags,
  selectedTag,
  selectedTagsForDelete,
  isLibraryExpanded,
  toggleLibraryExpanded,
  handleTagClick,
  handleTagContextMenu,
  handleNewTagClick,
  toggleSettingsDialog,
  books,
  onBookUpdate,
  onRefresh,
}: SidebarProps) {
  const location = useLocation();

  const actionButtons: ActionButtonItem[] = [
    // {
    //   label: "上传",
    //   icon: UploadIcon,
    //   onClick: triggerFileSelect,
    // },
    {
      label: "设置",
      icon: Settings,
      onClick: toggleSettingsDialog,
    },
  ];

  return (
    <aside className="z-40 m-1 mt-0 mr-0 flex h-[calc(100vh-48px)] w-48 flex-col overflow-hidden overflow-y-auto border-neutral-200">
      <div className="p-3 pt-3.5 pb-2">
        <SearchToggle searchQuery={searchQuery} onSearchChange={onSearchChange} />
      </div>

      <nav className="flex flex-1 flex-col space-y-1 px-2 py-4 pt-2">
        {navigationItems.map((item) => {
          const isActive = location.pathname === item.path;
          const Icon = item.icon;

          return (
            <div key={item.path}>
              {item.path === "/" ? (
                <button
                  onClick={toggleLibraryExpanded}
                  className={clsx(
                    "flex w-full items-center gap-2 rounded-md p-1 py-1 text-left text-sm transition-colors hover:bg-neutral-100 dark:hover:bg-neutral-700/50",
                    isActive ? "text-neutral-900 dark:text-neutral-100" : "text-neutral-700 dark:text-neutral-300",
                  )}
                >
                  {isLibraryExpanded ? (
                    <ChevronDown size={14} className="flex-shrink-0" />
                  ) : (
                    <ChevronRight size={14} className="flex-shrink-0" />
                  )}
                  <Icon size={16} className="flex-shrink-0" />
                  <span className="font-medium text-sm">{item.label}</span>
                </button>
              ) : (
                <Link
                  to={item.path}
                  className={clsx(
                    "flex w-full items-center gap-2 rounded-md p-1 py-1 text-left text-sm transition-colors hover:bg-neutral-100 dark:hover:bg-neutral-700/50",
                    isActive ? "text-neutral-900 dark:text-neutral-100" : "text-neutral-700 dark:text-neutral-300",
                  )}
                >
                  <Icon size={16} className="flex-shrink-0" />
                  <span className="font-medium text-sm">{item.label}</span>
                </Link>
              )}

              {item.path === "/" && isActive && isLibraryExpanded && (
                <TagList
                  tags={tags}
                  selectedTag={selectedTag}
                  selectedTagsForDelete={selectedTagsForDelete}
                  handleTagClick={handleTagClick}
                  handleTagContextMenu={handleTagContextMenu}
                  handleNewTagClick={handleNewTagClick}
                  books={books}
                  onBookUpdate={onBookUpdate}
                  onRefresh={onRefresh}
                />
              )}
            </div>
          );
        })}
      </nav>

      <div className="space-y-1 px-3 py-3">
        {actionButtons.map((button, index) => {
          const Icon = button.icon;

          return (
            <button
              key={index}
              onClick={button.onClick}
              className={clsx(
                "flex w-full items-center gap-2 rounded-sm p-2 py-1.5 text-left text-sm",
                "text-neutral-700 hover:bg-neutral-300 dark:text-neutral-300 dark:hover:bg-neutral-700",
              )}
            >
              <Icon size={16} className="flex-shrink-0" />
              <span className="text-sm">{button.label}</span>
            </button>
          );
        })}
      </div>
    </aside>
  );
}
