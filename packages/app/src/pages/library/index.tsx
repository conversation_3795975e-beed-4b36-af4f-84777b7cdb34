import Spinner from "@/components/Spinner";
import SettingsDialog from "@/components/settings/SettingsDialog";
import { Button } from "@/components/ui/button";
import { useEnv } from "@/context/EnvContext";
import { useBookUpload } from "@/hooks/useBookUpload";
import { useSafeAreaInsets } from "@/hooks/useSafeAreaInsets";
import { useScreenWakeLock } from "@/hooks/useScreenWakeLock";
import { useTheme } from "@/hooks/useTheme";
import { useTranslation } from "@/hooks/useTranslation";
import { useUICSS } from "@/hooks/useUICSS";
import { useAppSettingsStore } from "@/store/appSettingsStore";
import { useBookSettingsStore } from "@/store/bookSettingsStore";
import { useLibraryStore } from "@/store/libraryStore";
import clsx from "clsx";
import { Plus, Upload as UploadIcon } from "lucide-react";
import type React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import BookItem from "./components/BookItem";
import CreateTagDialog from "./components/CreateTagDialog";
import EditTagDialog from "./components/EditTagDialog";
import Sidebar from "./components/Sidebar";
import Upload from "./components/Upload";
import { useBooksFilter } from "./hooks/useBooksFilter";
import { useBooksOperations } from "./hooks/useBooksOperations";
import { useLibraryUI } from "./hooks/useLibraryUI";
import { useTagsManagement } from "./hooks/useTagsManagement";
import { useTagsOperations } from "./hooks/useTagsOperations";

export default function NewLibraryPage() {
  const { envConfig, appService } = useEnv();
  const { searchQuery, booksWithStatus, isLoading, refreshBooks, setSearchQuery } = useLibraryStore();
  const { isSettingsDialogOpen, toggleSettingsDialog } = useAppSettingsStore();
  const _ = useTranslation();
  const insets = useSafeAreaInsets();
  const { settings, setSettings } = useBookSettingsStore();
  const { isDragOver, isUploading, handleDragOver, handleDragLeave, handleDrop, triggerFileSelect } = useBookUpload();

  const isInitiating = useRef(false);
  const [libraryLoaded, setLibraryLoaded] = useState(false);
  const [selectedTagsForDelete, setSelectedTagsForDelete] = useState<string[]>([]);

  const { selectedTag, tags, filteredBooksByTag, handleTagSelect } = useTagsManagement(booksWithStatus);
  const { filteredBooks } = useBooksFilter(filteredBooksByTag, searchQuery);
  const {
    viewMode,
    isLibraryExpanded,
    showNewTagDialog,
    toggleLibraryExpanded,
    handleNewTagClick,
    handleCloseNewTagDialog,
  } = useLibraryUI();
  const { handleBookDelete, handleBookUpdate } = useBooksOperations(refreshBooks);

  useTheme({ systemUIVisible: true, appThemeColor: "base-200" });
  useUICSS();
  useScreenWakeLock(settings.screenWakeLock);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isInitiating.current) return;
    isInitiating.current = true;

    const initLibrary = async () => {
      try {
        const appService = await envConfig.getAppService();
        const settings = await appService.loadSettings();
        setSettings(settings);

        await refreshBooks();
        setLibraryLoaded(true);
      } catch (error) {
        console.error("Error initializing library:", error);
        setLibraryLoaded(true);
      }
    };

    initLibrary();
    return () => {
      isInitiating.current = false;
    };
  }, [refreshBooks]);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
    },
    [setSearchQuery],
  );

  const handleTagSelectWithSearch = useCallback(
    (tagId: string) => {
      handleTagSelect(tagId, () => setSearchQuery(""));
    },
    [handleTagSelect, setSearchQuery],
  );

  const handleTagClick = useCallback(
    (tagId: string, event: React.MouseEvent) => {
      if (event.shiftKey) {
        setSelectedTagsForDelete((prev) =>
          prev.includes(tagId) ? prev.filter((id) => id !== tagId) : [...prev, tagId],
        );
      } else {
        setSelectedTagsForDelete([]);
        handleTagSelectWithSearch(tagId);
      }
    },
    [handleTagSelectWithSearch],
  );

  const clearSelectedTags = useCallback(() => {
    setSelectedTagsForDelete([]);
  }, []);

  const { handleEditTagCancel, handleTagContextMenu, editingTag } = useTagsOperations({
    booksWithStatus,
    handleBookUpdate,
    refreshBooks,
    selectedTag,
    handleTagSelect,
    selectedTagsForDelete,
    tags,
    clearSelectedTags,
  });

  const visibleBooks = filteredBooks;
  const hasBooks = libraryLoaded && visibleBooks.length > 0;
  const hasLibraryBooks = libraryLoaded && booksWithStatus.length > 0;

  if (!appService || !insets || !libraryLoaded) {
    return null;
  }

  return (
    <div
      className={clsx(
        "flex h-dvh w-full bg-transparent transition-all duration-200",
        isDragOver && "bg-neutral-50 dark:bg-neutral-900/20",
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {isDragOver && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-neutral-50/80 backdrop-blur-sm dark:bg-neutral-900/40">
          <div className="flex flex-col items-center gap-4 rounded-2xl border-2 border-neutral-400 border-dashed bg-white/90 px-30 py-16 shadow-lg dark:border-neutral-500 dark:bg-neutral-800/90">
            <UploadIcon className="h-12 w-12 text-neutral-600 dark:text-neutral-400" />
            <div className="text-center">
              <h3 className="font-semibold text-lg text-neutral-900 dark:text-neutral-100">
                {_("Drop files to upload")}
              </h3>
              <p className="text-neutral-600 text-sm dark:text-neutral-400">{_("Release to upload your books")}</p>
            </div>
          </div>
        </div>
      )}

      <Sidebar
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
        tags={tags}
        selectedTag={selectedTag}
        selectedTagsForDelete={selectedTagsForDelete}
        isLibraryExpanded={isLibraryExpanded}
        toggleLibraryExpanded={toggleLibraryExpanded}
        handleTagClick={handleTagClick}
        handleTagContextMenu={handleTagContextMenu}
        handleNewTagClick={handleNewTagClick}
        triggerFileSelect={triggerFileSelect}
        toggleSettingsDialog={toggleSettingsDialog}
        books={booksWithStatus}
        onBookUpdate={handleBookUpdate}
        onRefresh={refreshBooks}
      />

      <div className="h-[calc(100vh-40px)] flex-1 p-1 pt-0">
        <div className="flex h-full flex-1 flex-col rounded-md border bg-background pt-2 shadow-around">
          <div className="mb-2 flex items-center justify-between px-4 pb-2">
            <h3 className="font-bold text-3xl dark:border-neutral-700">
              {selectedTag === "all" ? "我的图书" : tags.find((t) => t.id === selectedTag)?.name || "我的图书"}
            </h3>
            <Button onClick={triggerFileSelect} disabled={isUploading} variant="default" size="sm">
              {isUploading ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border border-white/30 border-t-white" />
                  {_("Uploading...")}
                </>
              ) : (
                <>
                  <Plus size={16} />
                  {_("Add Book")}
                </>
              )}
            </Button>
          </div>

          {isLoading && (
            <div className="fixed inset-0 z-50 flex items-center justify-center">
              <Spinner loading />
            </div>
          )}

          {hasBooks ? (
            <div className="mb-1 flex-1 overflow-auto px-4 pb-8">
              <div className="mx-auto">
                {searchQuery.trim() && (
                  <div className="mb-4 text-base-content/70 text-sm">
                    {_("Found {{count}} book(s) for '{{query}}'", { count: visibleBooks.length, query: searchQuery })}
                  </div>
                )}

                {viewMode === "grid" ? (
                  <div className="grid 3xl:grid-cols-8 grid-cols-3 gap-4 sm:grid-cols-5 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7">
                    {visibleBooks.map((book) => (
                      <BookItem
                        key={book.id}
                        book={book}
                        viewMode={viewMode}
                        availableTags={tags}
                        onDelete={handleBookDelete}
                        onUpdate={handleBookUpdate}
                        onRefresh={refreshBooks}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {visibleBooks.map((book) => (
                      <BookItem
                        key={book.id}
                        book={book}
                        viewMode={viewMode}
                        availableTags={tags}
                        onDelete={handleBookDelete}
                        onUpdate={handleBookUpdate}
                        onRefresh={refreshBooks}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
          ) : hasLibraryBooks && searchQuery.trim() ? (
            <div className="flex flex-1 flex-col items-center justify-center p-8 text-center">
              <div className="text-base-content/50 text-lg">
                {_("No books found for '{{query}}'", { query: searchQuery })}
              </div>
              <div className="mt-2 text-base-content/40 text-sm">{_("Try searching with different keywords")}</div>
            </div>
          ) : (
            <div className="flex-1">
              <Upload />
            </div>
          )}
        </div>
      </div>

      <CreateTagDialog
        isOpen={showNewTagDialog}
        onClose={handleCloseNewTagDialog}
        books={booksWithStatus}
        selectedTag={selectedTag}
        filteredBooksByTag={filteredBooksByTag}
        onBookUpdate={handleBookUpdate}
        onRefreshBooks={refreshBooks}
      />

      <EditTagDialog
        isOpen={!!editingTag}
        onClose={handleEditTagCancel}
        tag={editingTag}
        books={booksWithStatus}
        onBookUpdate={handleBookUpdate}
        onRefreshBooks={refreshBooks}
      />

      <SettingsDialog open={isSettingsDialogOpen} onOpenChange={toggleSettingsDialog} />
    </div>
  );
}
