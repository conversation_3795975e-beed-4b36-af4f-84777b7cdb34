import { transformContent } from "@/services/transformService";
import { useReaderStore } from "@/store/readerStore";
import { eventDispatcher } from "@/utils/event";
import { type TextSelection, getTextFromRange } from "@/utils/sel";
import { useAtomValue } from "jotai";
import { useEffect, useRef } from "react";
import { bookDataAtom, viewStateAtom } from "../atoms/readerAtoms";

export const useTextSelector = (
  bookKey: string,
  setSelection: React.Dispatch<React.SetStateAction<TextSelection | null>>,
  handleDismissPopup: () => void,
) => {
  const { getView } = useReaderStore();
  const jotaiViewState = useAtomValue(viewStateAtom);
  const jotaiBookData = useAtomValue(bookDataAtom);
  const view = getView(bookKey);
  const bookData = jotaiBookData?.book;
  const primaryLang = bookData?.primaryLanguage || "en";

  const isPopupVisible = useRef(false);
  const popupShowTime = useRef<number>(0);
  const POPUP_DEBOUNCE_TIME = 300;

  const isValidSelection = (sel: Selection) => {
    return sel && sel.toString().trim().length > 0 && sel.rangeCount > 0;
  };

  const transformCtx = {
    bookKey,
    viewSettings: jotaiViewState?.viewSettings,
    content: "",
    transformers: ["punctuation"],
    reversePunctuationTransform: true,
  };

  const getAnnotationText = async (range: Range) => {
    transformCtx.content = getTextFromRange(range, primaryLang.startsWith("ja") ? ["rt"] : []);
    return await transformContent(transformCtx);
  };

  const makeSelection = async (sel: Selection, index: number) => {
    const range = sel.getRangeAt(0);
    setSelection({ key: bookKey, text: await getAnnotationText(range), range, index });
  };

  const handleMouseUp = (doc: Document, index: number) => {
    const sel = doc.getSelection() as Selection;
    if (isValidSelection(sel)) {
      makeSelection(sel, index);
    }
  };

  const handleScroll = () => {
    handleDismissPopup();
  };

  const handleShowPopup = (showPopup: boolean) => {
    isPopupVisible.current = showPopup;
    if (showPopup) {
      // 记录弹框显示时间，用于防抖
      popupShowTime.current = Date.now();
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const handleSingleClick = (): boolean => {
      if (isPopupVisible.current) {
        // 检查是否在防抖时间内，如果是则不关闭弹框
        const timeSincePopupShow = Date.now() - popupShowTime.current;
        if (timeSincePopupShow < POPUP_DEBOUNCE_TIME) {
          // 消费掉这个事件，不让它继续传播
          return true;
        }

        handleDismissPopup();
        view?.deselect();
        isPopupVisible.current = false;
        return true;
      }
      return false;
    };

    eventDispatcher.onSync("iframe-single-click", handleSingleClick);
    return () => {
      eventDispatcher.offSync("iframe-single-click", handleSingleClick);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    handleScroll,
    handleMouseUp,
    handleShowPopup,
  };
};
