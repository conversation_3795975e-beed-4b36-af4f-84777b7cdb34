import { useEnv } from "@/context/EnvContext";
import { useUICSS } from "@/hooks/useUICSS";
import { type BookDoc, getDirection } from "@/libs/document";
import { transformContent } from "@/services/transformService";
import { useBookDataStore } from "@/store/bookDataStore";
import { useParallelViewStore } from "@/store/parallelViewStore";
import { useReaderStore } from "@/store/readerStore";
import { useSidebarStore } from "@/store/sidebarStore";
import { useThemeStore } from "@/store/themeStore";
import type { BookConfig } from "@/types/book";
import type { Insets } from "@/types/misc";
import { type FoliateView, wrappedFoliateView } from "@/types/view";
import { getBookDirFromLanguage, getBookDirFromWritingMode } from "@/utils/book";
import { getMaxInlineSize } from "@/utils/config";
import { mountAdditionalFonts } from "@/utils/font";
import { manageSyntaxHighlighting } from "@/utils/highlightjs";
import { isCJKLang } from "@/utils/lang";
import { getDirFromUILanguage } from "@/utils/rtl";
import {
  applyFixedlayoutStyles,
  applyImageStyle,
  applyTranslationStyle,
  getStyles,
  transformStylesheet,
} from "@/utils/style";
import { useAtomValue, useSetAtom } from "jotai";
import { type RefObject, useCallback, useEffect, useRef, useState } from "react";
import { progressAtom, viewAtom, viewSettingsAtom } from "../atoms/readerAtoms";
import {
  handleClick,
  handleKeydown,
  handleMouseMove,
  handleMousedown,
  handleMouseup,
  handleTouchEnd,
  handleTouchMove,
  handleTouchStart,
  handleWheel,
} from "../utils/iframeEventHandlers";
import { useFoliateEvents } from "./useFoliateEvents";
import { useMouseEvent, useTouchEvent } from "./useIframeEvents";
import { usePagination } from "./usePagination";
import { useProgressAutoSave } from "./useProgressAutoSave";

export const useFoliateViewer = (bookKey: string, bookDoc: BookDoc, config: BookConfig, insets: Insets) => {
  const { getView, getViewSettings, setView: setFoliateView, setProgress, setViewSettings } = useReaderStore();
  const jotaiViewSettings = useAtomValue(viewSettingsAtom);
  const setJotaiView = useSetAtom(viewAtom);
  const setJotaiProgress = useSetAtom(progressAtom);
  const setJotaiViewSettings = useSetAtom(viewSettingsAtom);
  const { getParallels } = useParallelViewStore();
  const { getBookData } = useBookDataStore();
  const { appService } = useEnv();
  const { themeCode, isDarkMode } = useThemeStore();
  const { setSideBarBookKey } = useSidebarStore();

  const viewRef = useRef<FoliateView | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isViewCreated = useRef(false);
  const [toastMessage, setToastMessage] = useState("");

  // expose styles for UI
  useUICSS(bookKey);
  useProgressAutoSave(bookKey);

  // dynamic container size
  const getContainerDimensions = useCallback(() => {
    if (!containerRef.current) {
      const viewWidth = appService?.isMobile ? screen.width : window.innerWidth;
      const viewHeight = appService?.isMobile ? screen.height : window.innerHeight;
      return { width: viewWidth - insets.left - insets.right, height: viewHeight - insets.top - insets.bottom };
    }
    const rect = containerRef.current.getBoundingClientRect();
    return { width: rect.width - insets.left - insets.right, height: rect.height - insets.top - insets.bottom };
  }, [appService?.isMobile, insets]);

  // update renderer styles/sizing
  const performUpdate = useCallback(
    (dimensions: { width: number; height: number }) => {
      try {
        if (!viewRef.current?.renderer) return;
        // prefer latest settings from store (synchronous), fallback to jotai snapshot
        const viewSettings = getViewSettings?.(bookKey) ?? jotaiViewSettings;
        if (!viewSettings) return;
        // compute attributes considering column mode
        const isVertical = !!viewSettings.vertical;
        const containerSize = isVertical ? dimensions.height : dimensions.width;

        // compute gap in px (same formula as paginator.js)
        const g = (viewSettings.gapPercent ?? 7) / 100;
        const gapPx = (-g / (g - 1)) * containerSize;

        let computedMaxInlineSize = getMaxInlineSize(viewSettings);
        let computedMaxColumnCount = viewSettings.maxColumnCount ?? 2;
        const columnMode = (viewSettings as any).columnMode ?? "auto";

        if (!viewSettings.scrolled) {
          if (columnMode === "one") {
            computedMaxColumnCount = 1;
            // ensure divisor becomes 1 by making MIS >= container size
            computedMaxInlineSize = Math.max(containerSize, 2000);
          } else if (columnMode === "two") {
            computedMaxColumnCount = 2;
            // aim for two columns: MIS <= (size/2 - gap)
            const target = Math.floor(containerSize / 2 - gapPx);
            computedMaxInlineSize = Math.max(120, target);
          }
        }

        viewRef.current.renderer.setAttribute("max-column-count", String(computedMaxColumnCount));
        viewRef.current.renderer.setAttribute("max-inline-size", `${computedMaxInlineSize}px`);
        viewRef.current.renderer.setStyles?.(getStyles(viewSettings));
        if (typeof applyMarginAndGap === "function") applyMarginAndGap();
      } catch (error) {
        console.error("Error in performUpdate:", error);
      }
    },
    [jotaiViewSettings, getViewSettings, bookKey],
  );

  const manualUpdate = useCallback(() => {
    const dimensions = getContainerDimensions();
    performUpdate(dimensions);
  }, [getContainerDimensions, performUpdate]);

  useEffect(() => {
    const handleFoliateResize = (event: CustomEvent) => {
      const { bookIds } = event.detail;
      if (bookIds?.includes(bookKey)) setTimeout(() => manualUpdate(), 100);
    };
    window.addEventListener("foliate-resize-update", handleFoliateResize as EventListener);
    return () => window.removeEventListener("foliate-resize-update", handleFoliateResize as EventListener);
  }, [bookKey, manualUpdate]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const timer = setTimeout(() => setToastMessage(""), 2000);
    return () => clearTimeout(timer);
  }, [toastMessage]);

  const progressRelocateHandler = (event: Event) => {
    const detail = (event as CustomEvent).detail;
    setProgress(bookKey, detail.cfi, detail.tocItem, detail.section, detail.location, detail.time, detail.range);
    setJotaiProgress({
      location: detail.cfi,
      sectionHref: detail.tocItem?.href || "",
      sectionLabel: detail.tocItem?.label || "",
      sectionId: detail.tocItem?.id ?? 0,
      section: detail.section,
      pageinfo: detail.location,
      timeinfo: detail.time,
      range: detail.range,
    });
  };

  const getDocTransformHandler = ({ width, height }: { width: number; height: number }) => {
    return (event: Event) => {
      const { detail } = event as CustomEvent;
      detail.data = Promise.resolve(detail.data)
        .then((data) => {
          const viewSettings = jotaiViewSettings;
          if (viewSettings && detail.type === "text/css") return transformStylesheet(width, height, data);
          if (viewSettings && detail.type === "application/xhtml+xml") {
            const ctx = {
              bookKey,
              viewSettings,
              content: data,
              transformers: ["punctuation", "footnote"],
            };
            return Promise.resolve(transformContent(ctx));
          }
          return data;
        })
        .catch((e) => {
          console.error(new Error(`Failed to load ${detail.name}`, { cause: e }));
          return "";
        });
    };
  };

  const docLoadHandler = (event: Event) => {
    const detail = (event as CustomEvent).detail;
    if (detail.doc) {
      const writingDir = viewRef.current?.renderer.setStyles && getDirection(detail.doc);
      const viewSettings = jotaiViewSettings;
      if (!viewSettings) return;
      const bookData = getBookData(bookKey)!;
      viewSettings.vertical = writingDir?.vertical || viewSettings.writingMode.includes("vertical") || false;
      viewSettings.rtl =
        writingDir?.rtl || getDirFromUILanguage() === "rtl" || viewSettings.writingMode.includes("rl") || false;
      setViewSettings(bookKey, { ...viewSettings });
      setJotaiViewSettings({ ...viewSettings });

      mountAdditionalFonts(detail.doc, isCJKLang(bookData.book?.primaryLanguage));

      if (bookDoc.rendition?.layout === "pre-paginated") applyFixedlayoutStyles(detail.doc, viewSettings);

      applyImageStyle(detail.doc);

      if (viewSettings.codeHighlighting) manageSyntaxHighlighting(detail.doc, viewSettings);

      if (!detail.doc.isEventListenersAdded) {
        // listened events in iframes are posted to the main window
        // and then used by useMouseEvent and useTouchEvent
        // and more gesture events can be detected in the iframeEventHandlers
        detail.doc.isEventListenersAdded = true;
        detail.doc.addEventListener("keydown", handleKeydown.bind(null, bookKey));
        detail.doc.addEventListener("mousedown", handleMousedown.bind(null, bookKey));
        detail.doc.addEventListener("mouseup", handleMouseup.bind(null, bookKey));
        detail.doc.addEventListener("mousemove", handleMouseMove.bind(null, bookKey));
        detail.doc.addEventListener("click", handleClick.bind(null, bookKey));
        detail.doc.addEventListener("wheel", handleWheel.bind(null, bookKey));
        detail.doc.addEventListener("touchstart", handleTouchStart.bind(null, bookKey));
        detail.doc.addEventListener("touchmove", handleTouchMove.bind(null, bookKey));
        detail.doc.addEventListener("touchend", handleTouchEnd.bind(null, bookKey));
      }
    }
  };

  const docRelocateHandler = (event: Event) => {
    const detail = (event as CustomEvent).detail;
    if (detail.reason !== "scroll" && detail.reason !== "page") return;
    const parallelViews = getParallels(bookKey);
    if (parallelViews && parallelViews.size > 0) {
      parallelViews.forEach((key) => {
        if (key !== bookKey) getView(key)?.renderer.goTo?.({ index: detail.index, anchor: detail.fraction });
      });
    }
  };

  const { handlePageFlip, handleContinuousScroll } = usePagination(
    bookKey,
    viewRef,
    containerRef as RefObject<HTMLDivElement>,
  );
  const mouseHandlers = useMouseEvent(bookKey, handlePageFlip, handleContinuousScroll);
  const touchHandlers = useTouchEvent(bookKey, handleContinuousScroll);

  useFoliateEvents(viewRef.current, {
    onLoad: docLoadHandler,
    onRelocate: progressRelocateHandler,
    onRendererRelocate: docRelocateHandler,
  });

  // Clear search highlights on a single click inside the iframe for this book
  useEffect(() => {
    const handleIframeSingleClick = (event: MessageEvent) => {
      if (event?.data?.type === "iframe-single-click" && event?.data?.bookKey === bookKey) {
        try {
          getView(bookKey)?.clearSearch();
        } catch (e) {
          console.warn("Failed to clear search on single click:", e);
        }
      }
    };
    window.addEventListener("message", handleIframeSingleClick);
    return () => window.removeEventListener("message", handleIframeSingleClick);
  }, [bookKey, getView]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isViewCreated.current) return;
    isViewCreated.current = true;

    const openBook = async () => {
      setSideBarBookKey(bookKey);
      await import("foliate-js/view.js");
      const view = wrappedFoliateView(document.createElement("foliate-view") as FoliateView);
      view.id = `foliate-view-${bookKey}`;
      document.body.append(view);
      containerRef.current?.appendChild(view);

      const viewSettings = jotaiViewSettings;
      if (!viewSettings) return;
      const writingMode = viewSettings.writingMode;
      if (writingMode) {
        const settingsDir = getBookDirFromWritingMode(writingMode);
        const languageDir = getBookDirFromLanguage(bookDoc.metadata.language);
        if (settingsDir !== "auto") bookDoc.dir = settingsDir;
        else if (languageDir !== "auto") bookDoc.dir = languageDir;
      }

      await view.open(bookDoc);
      viewRef.current = view;
      setFoliateView(bookKey, view);
      setJotaiView(view);

      const { book } = view;
      book.transformTarget?.addEventListener("load", (event: Event) => {
        const { detail } = event as CustomEvent;
        if (detail.isScript) detail.allowScript = viewSettings.allowScript ?? false;
      });
      const { width, height } = getContainerDimensions();
      book.transformTarget?.addEventListener("data", getDocTransformHandler({ width, height }));
      view.renderer.setStyles?.(getStyles(viewSettings));
      applyTranslationStyle(viewSettings);

      const animated = viewSettings.animated!;
      const maxColumnCount = viewSettings.maxColumnCount!;
      const maxInlineSize = getMaxInlineSize(viewSettings);
      const maxBlockSize = viewSettings.maxBlockSize!;
      if (animated) view.renderer.setAttribute("animated", "");
      else view.renderer.removeAttribute("animated");
      view.renderer.setAttribute("max-column-count", maxColumnCount);
      view.renderer.setAttribute("max-inline-size", `${maxInlineSize}px`);
      view.renderer.setAttribute("max-block-size", `${maxBlockSize}px`);
      // view.renderer.setAttribute("gap", "4%");
      applyMarginAndGap();

      const lastLocation = config.location;
      if (lastLocation) await view.init({ lastLocation });
      else await view.goToFraction(0);

      manualUpdate();
    };

    openBook();

    return () => {
      if (viewRef.current) {
        try {
          const view = viewRef.current;
          viewRef.current = null;
          isViewCreated.current = false;
          setTimeout(() => {
            try {
              if (view.close) view.close();
              if (view.remove) view.remove();
            } catch (cleanupError) {
              console.warn("Error in delayed cleanup:", cleanupError);
            }
          }, 50);
        } catch (error) {
          console.warn("Error during foliate view cleanup:", error);
          viewRef.current = null;
          isViewCreated.current = false;
        }
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const applyMarginAndGap = () => {
    const viewSettings = jotaiViewSettings;
    if (!viewSettings) return;
    if (viewSettings.scrolled) viewRef.current?.renderer.setAttribute("flow", "scrolled");
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (viewRef.current?.renderer) {
      const viewSettings = jotaiViewSettings;
      if (!viewSettings) return;
      viewRef.current.renderer.setStyles?.(getStyles(viewSettings));
      if (bookDoc.rendition?.layout === "pre-paginated") {
        const docs = viewRef.current.renderer.getContents();
        docs.forEach(({ doc }) => applyFixedlayoutStyles(doc, viewSettings));
      }
    }
  }, [themeCode, isDarkMode, jotaiViewSettings?.overrideColor, jotaiViewSettings?.invertImgColorInDark]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (viewRef.current?.renderer && jotaiViewSettings) applyMarginAndGap();
  }, [
    insets.top,
    insets.right,
    insets.bottom,
    insets.left,
    jotaiViewSettings?.doubleBorder,
    jotaiViewSettings?.showHeader,
    jotaiViewSettings?.showFooter,
  ]);

  // Return the container ref and event handlers to attach in the component
  return { containerRef, mouseHandlers, touchHandlers } as const;
};

export default useFoliateViewer;
