import Spinner from "@/components/Spinner";
import { useEnv } from "@/context/EnvContext";
import { useActiveBookStore } from "@/store/activeBookStore";
import { useBookDataStore } from "@/store/bookDataStore";
import { useBookSettingsStore } from "@/store/bookSettingsStore";
import { useReaderStore } from "@/store/readerStore";
import { useSidebarStore } from "@/store/sidebarStore";
import type { SystemSettings } from "@/types/settings";
import { eventDispatcher } from "@/utils/event";
import { navigateToLibrary } from "@/utils/nav";
import { throttle } from "@/utils/throttle";
import { useAtomValue, useSetAtom } from "jotai";
import type * as React from "react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { bookKeyAtom } from "../atoms/bookAtoms";
import { bookData<PERSON>tom, initBookDataAction<PERSON>tom, viewSettingsAtom } from "../atoms/readerAtoms";
import useBookShortcuts from "../hooks/useBookShortcuts";
import useBooksManager from "../hooks/useBooksManager";
import BookGrid from "./BookGrid";

const ReaderContent: React.FC<{ id?: string; settings: SystemSettings }> = ({ id, settings }) => {
  const navigate = useNavigate();
  const { envConfig } = useEnv();
  const setJotaiBookKey = useSetAtom(bookKeyAtom);
  const jotaiBookKey = useAtomValue(bookKeyAtom);
  const jotaiBookData = useAtomValue(bookDataAtom);
  const jotaiViewSettings = useAtomValue(viewSettingsAtom);
  const initBookData = useSetAtom(initBookDataActionAtom);
  const { bookKeys, dismissBook, getNextBookKey } = useBooksManager();
  const { sideBarBookKey, setSideBarBookKey } = useSidebarStore();
  const { saveSettings } = useBookSettingsStore();
  const { getConfig, getBookData, saveConfig } = useBookDataStore();
  const { getView, setBookKeys } = useReaderStore();
  const { initViewState, getViewState, clearViewState } = useReaderStore();
  const [loading, setLoading] = useState(false);

  useBookShortcuts({ sideBarBookKey, bookKeys });

  // 当 id 变化时，刷新 Reader 子树内的 jotai store 状态
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (!id) return;
    setLoading(false);

    setJotaiBookKey(id);
    setBookKeys([id]);

    // 同步当前书籍到全局（Zustand）供工具/侧边聊天读取
    useActiveBookStore.getState().setActiveBookId(id);

    initBookData({ envConfig, id, key: id }).catch((error: any) => {
      console.log("Error initializing jotai book", id, error);
    });

    if (!getViewState(id)) {
      initViewState(envConfig, id, id, true).catch((error: any) => {
        console.log("Error initializing book", id, error);
      });
    }
    setSideBarBookKey(id);
  }, [id]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    window.addEventListener("beforeunload", handleCloseBooks);
    eventDispatcher.on("beforereload", handleCloseBooks);
    eventDispatcher.on("quit-app", handleCloseBooks);
    return () => {
      window.removeEventListener("beforeunload", handleCloseBooks);
      eventDispatcher.off("beforereload", handleCloseBooks);
      eventDispatcher.off("quit-app", handleCloseBooks);
    };
  }, [bookKeys]);

  const saveBookConfig = async (bookKey: string) => {
    const config = getConfig(bookKey);
    const { book } = getBookData(bookKey) || {};
    if (book && config) {
      eventDispatcher.dispatch("sync-book-progress", { bookKey });
      const settings = useBookSettingsStore.getState().settings;
      await saveConfig(envConfig, bookKey, config, settings);
    }
  };

  const saveConfigAndCloseBook = async (bookKey: string) => {
    console.log("Closing book", bookKey);
    try {
      getView(bookKey)?.close();
      getView(bookKey)?.remove();
    } catch {
      console.info("Error closing book", bookKey);
    }
    eventDispatcher.dispatch("tts-stop", { bookKey });
    await saveBookConfig(bookKey);
    clearViewState(bookKey);
  };

  const saveSettingsAndGoToLibrary = () => {
    saveSettings(envConfig, settings);
    navigateToLibrary(navigate);
  };

  const handleCloseBooks = throttle(async () => {
    const settings = useBookSettingsStore.getState().settings;
    await Promise.all(bookKeys.map((key: string) => saveConfigAndCloseBook(key)));
    await saveSettings(envConfig, settings);
  }, 200);

  const handleCloseBook = async (bookKey: string) => {
    saveConfigAndCloseBook(bookKey);
    if (sideBarBookKey === bookKey) {
      setSideBarBookKey(getNextBookKey(sideBarBookKey));
    }
    dismissBook(bookKey);
    if (bookKeys.filter((key: string) => key !== bookKey).length === 0) {
      saveSettingsAndGoToLibrary();
    }
  };

  if (!jotaiBookKey || !jotaiViewSettings) return null;

  if (!jotaiBookData || !jotaiBookData.book || !jotaiBookData.bookDoc) {
    setTimeout(() => setLoading(true), 300);
    return (
      loading && (
        <div className="reader-content flex h-dvh">
          <Spinner loading={true} />
        </div>
      )
    );
  }

  return (
    <div className="reader-content flex h-[calc(100vh-50px)] rounded-md">
      <BookGrid bookKey={jotaiBookKey} onCloseBook={handleCloseBook} />
    </div>
  );
};

export default ReaderContent;
