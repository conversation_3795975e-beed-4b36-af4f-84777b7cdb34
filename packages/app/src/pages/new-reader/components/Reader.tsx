import { useEnv } from "@/context/EnvContext";
import { getBooksWithStatus } from "@/services/bookService";
import { useBookSettingsStore } from "@/store/bookSettingsStore";
import { useLibraryStore } from "@/store/libraryStore";
import { mountAdditionalFonts } from "@/utils/font";
import { getLocale } from "@/utils/misc";
import { initDayjs } from "@/utils/time";
import { useAtom } from "jotai";
import type * as React from "react";
import { Suspense, useEffect, useRef, useState } from "react";
import { bookSettingsAtom } from "../atoms/bookAtoms";
import { convertBookWithStatusToBook } from "../utils/bookConvert";
import ReaderContent from "./ReaderContent";

const Reader: React.FC<{ id?: string }> = ({ id }) => {
  const { envConfig } = useEnv();
  const { setLibrary } = useLibraryStore();
  const [jotaiSettings, setJotaiSettings] = useAtom(bookSettingsAtom);
  const { settings, setSettings } = useBookSettingsStore();
  const [libraryLoaded, setLibraryLoaded] = useState(false);
  const isInitiating = useRef(false);

  useEffect(() => {
    mountAdditionalFonts(document);
    initDayjs(getLocale());
  }, []);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isInitiating.current) return;
    isInitiating.current = true;
    const initLibrary = async () => {
      try {
        const appService = await envConfig.getAppService();
        const settings = await appService.loadSettings();
        setSettings(settings);
        setJotaiSettings(settings);
        const booksWithStatus = await getBooksWithStatus();
        const convertedBooks = await Promise.all(booksWithStatus.map(convertBookWithStatusToBook));
        setLibrary(convertedBooks);
        setLibraryLoaded(true);
      } catch (error) {
        console.error("Error loading library for reader:", error);
        setLibraryLoaded(true);
      }
    };

    initLibrary();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    libraryLoaded &&
    jotaiSettings.globalReadSettings && (
      <div className="reader-page select-none overflow-hidden rounded-md text-base-content">
        <Suspense>
          <ReaderContent id={id} settings={settings} />
        </Suspense>
      </div>
    )
  );
};

export default Reader;
