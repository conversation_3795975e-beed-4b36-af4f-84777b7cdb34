import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useTranslation } from "@/hooks/useTranslation";
import { useReaderStore } from "@/store/readerStore";
import type { BookSearchResult } from "@/types/book";
import { useAtomValue } from "jotai";
import { Search } from "lucide-react";
import type React from "react";
import { useCallback, useState } from "react";
import { bookDataAtom } from "../atoms/readerAtoms";
import SearchBar from "./sidebar/SearchBar";
import SearchResults from "./sidebar/SearchResults";

interface SearchDropdownProps {
  bookKey: string;
  onNavigate?: () => void;
}

const SearchDropdown: React.FC<SearchDropdownProps> = ({ bookKey, onNavigate }) => {
  const _ = useTranslation();
  const { getView } = useReaderStore();
  const jotaiBookData = useAtomValue(bookDataAtom);
  const [isSearchDropdownOpen, setIsSearchDropdownOpen] = useState(false);
  const [searchResults, setSearchResults] = useState<BookSearchResult[] | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [hasSearched, setHasSearched] = useState(false);

  // Get book data to check if search is available
  const bookData = jotaiBookData?.book;
  if (!bookData) {
    return null;
  }

  const handleToggleSearchDropdown = (isOpen: boolean) => {
    setIsSearchDropdownOpen(isOpen);
    if (!isOpen) {
      // Clear search results but keep highlighting for user reference
      setSearchResults(null);
      setSearchTerm("");
      setHasSearched(false);
    }
  };

  const handleSearchResultClick = useCallback(
    (cfi: string) => {
      setIsSearchDropdownOpen(false);
      setSearchResults(null);
      setSearchTerm("");
      onNavigate?.();

      // Navigate to the search result using the view instance
      getView(bookKey)?.goTo(cfi);

      // Set up one-time click listener to clear search highlighting when user clicks on page
      const view = getView(bookKey);
      if (view) {
        const clearSearchOnClick = () => {
          view.clearSearch();
          window.removeEventListener("message", handleIframeClick);
        };

        const handleIframeClick = (event: MessageEvent) => {
          if (event.data?.type === "iframe-single-click" && event.data?.bookKey === bookKey) {
            clearSearchOnClick();
          }
        };

        window.addEventListener("message", handleIframeClick);
      }
    },
    [bookKey, onNavigate, getView],
  );

  const handleSearchResultChange = useCallback((results: BookSearchResult[]) => {
    setHasSearched(true);
    setSearchResults(results);
  }, []);

  const handleSearchTermChange = useCallback((term: string) => {
    setSearchTerm(term);
    // Reset search state when search term is cleared
    if (!term.trim()) {
      setHasSearched(false);
      setSearchResults(null);
    }
  }, []);

  const handleHideSearchBar = useCallback(() => {
    setIsSearchDropdownOpen(false);
    setSearchResults(null);
    setSearchTerm("");
    setHasSearched(false);
  }, []);

  return (
    <DropdownMenu open={isSearchDropdownOpen} onOpenChange={handleToggleSearchDropdown}>
      <DropdownMenuTrigger asChild>
        <button
          className="btn btn-ghost flex items-center justify-center rounded-full p-0 focus:outline-0"
          title={_("Search")}
        >
          <Search size={18} />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80 p-0" align="end" side="bottom" sideOffset={4}>
        <div className="flex max-h-[calc(100vh-8rem)] flex-col">
          {/* Fixed search bar at top */}
          <div className="sticky top-0 z-10 flex-shrink-0">
            <SearchBar
              isVisible={isSearchDropdownOpen}
              bookKey={bookKey}
              searchTerm={searchTerm}
              onSearchResultChange={handleSearchResultChange}
              onSearchTermChange={handleSearchTermChange}
              onHideSearchBar={handleHideSearchBar}
            />
          </div>

          {/* Scrollable results area */}
          <div className="flex-1 overflow-y-auto">
            {searchResults && searchResults.length > 0 ? (
              <div className="h-full overflow-y-auto">
                <SearchResults bookKey={bookKey} results={searchResults} onSelectResult={handleSearchResultClick} />
              </div>
            ) : hasSearched && searchResults && searchResults.length === 0 ? (
              <div className="flex h-full items-center justify-center">
                <div className="p-4 text-center text-muted-foreground text-sm">{_("No search results found")}</div>
              </div>
            ) : (
              <div className="flex h-full items-center justify-center">
                <div className="p-4 text-center text-muted-foreground text-sm">
                  {_("Enter search terms to find content")}
                </div>
              </div>
            )}
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default SearchDropdown;
