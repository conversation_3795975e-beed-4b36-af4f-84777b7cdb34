import { useEnv } from "@/context/EnvContext";
import { useTranslation } from "@/hooks/useTranslation";
import { HIGHLIGHT_COLOR_HEX } from "@/services/constants";
import { iframeService } from "@/services/iframeService";
import { useBookDataStore } from "@/store/bookDataStore";
import { useBookSettingsStore } from "@/store/bookSettingsStore";
import { useReaderStore } from "@/store/readerStore";
import type { BookNote, HighlightColor, HighlightStyle } from "@/types/book";
import { eventDispatcher } from "@/utils/event";
import { uniqueId } from "@/utils/misc";
import { type Position, type TextSelection, getPopupPosition, getPosition } from "@/utils/sel";
import * as CFI from "foliate-js/epubcfi.js";
import { Overlayer } from "foliate-js/overlayer.js";
import type React from "react";
import { useCallback, useEffect, useState } from "react";
import { FiMessageCircle, FiSearch } from "react-icons/fi";
import { FiCopy } from "react-icons/fi";
import { FiHelpCircle } from "react-icons/fi";
import { PiHighlighterFill } from "react-icons/pi";
import { RiDeleteBinLine } from "react-icons/ri";
import { toast } from "sonner";
import { useFoliateEvents } from "../../hooks/useFoliateEvents";
import { useTextSelector } from "../../hooks/useTextSelector";
import AnnotationPopup from "./AnnotationPopup";
import AskAIPopup from "./AskAIPopup";

const Annotator: React.FC<{ bookKey: string }> = ({ bookKey }) => {
  const _ = useTranslation();
  const { envConfig } = useEnv();
  const { settings } = useBookSettingsStore();
  const { getConfig, saveConfig, getBookData, updateBooknotes } = useBookDataStore();
  const { getProgress, getView, getViewsById, getViewSettings } = useReaderStore();

  const config = getConfig(bookKey)!;
  const progress = getProgress(bookKey)!;
  const bookData = getBookData(bookKey)!;
  const view = getView(bookKey);
  const viewSettings = getViewSettings(bookKey)!;

  const [selection, setSelection] = useState<TextSelection | null>(null);
  const [showAnnotPopup, setShowAnnotPopup] = useState(false);
  const [showAskAIPopup, setShowAskAIPopup] = useState(false);
  const [trianglePosition, setTrianglePosition] = useState<Position>();
  const [annotPopupPosition, setAnnotPopupPosition] = useState<Position>();
  const [askAIPopupPosition, setAskAIPopupPosition] = useState<Position>();
  const [highlightOptionsVisible, setHighlightOptionsVisible] = useState(false);

  const [selectedStyle, setSelectedStyle] = useState<HighlightStyle>(settings.globalReadSettings.highlightStyle);
  const [selectedColor, setSelectedColor] = useState<HighlightColor>(
    settings.globalReadSettings.highlightStyles[selectedStyle],
  );

  const popupPadding = 10;
  const annotPopupWidth = Math.min(280, window.innerWidth - 2 * popupPadding);
  const annotPopupHeight = 36;

  const handleDismissPopup = useCallback(() => {
    setSelection(null);
    setShowAnnotPopup(false);
    setShowAskAIPopup(false);
  }, []);

  const handleDismissPopupAndSelection = () => {
    handleDismissPopup();
    view?.deselect();
  };

  const { handleScroll, handleMouseUp, handleShowPopup } = useTextSelector(bookKey, setSelection, handleDismissPopup);

  const onLoad = (event: Event) => {
    const detail = (event as CustomEvent).detail;
    const { doc, index } = detail;

    if (bookData.book?.format !== "PDF") {
      view?.renderer?.addEventListener("scroll", handleScroll);
      detail.doc?.addEventListener("mouseup", () => handleMouseUp(doc, index));
    }
  };

  const onDrawAnnotation = (event: Event) => {
    const viewSettings = getViewSettings(bookKey)!;
    const detail = (event as CustomEvent).detail;
    const { draw, annotation, doc, range } = detail;
    const { style, color } = annotation as BookNote;
    const hexColor = color ? HIGHLIGHT_COLOR_HEX[color] : color;
    if (style === "highlight") {
      draw(Overlayer.highlight, { color: hexColor });
    } else if (["underline", "squiggly"].includes(style as string)) {
      const { defaultView } = doc;
      const node = range.startContainer;
      const el = node.nodeType === 1 ? node : node.parentElement;
      const { writingMode, lineHeight, fontSize } = defaultView.getComputedStyle(el);
      const lineHeightValue = Number.parseFloat(lineHeight) || viewSettings.lineHeight * viewSettings.defaultFontSize;
      const fontSizeValue = Number.parseFloat(fontSize) || viewSettings.defaultFontSize;
      const strokeWidth = 2;
      const padding = viewSettings.vertical ? (lineHeightValue - fontSizeValue - strokeWidth) / 2 : strokeWidth;
      draw(Overlayer[style as keyof typeof Overlayer], { writingMode, color: hexColor, padding });
    }
  };

  const onShowAnnotation = (event: Event) => {
    const detail = (event as CustomEvent).detail;
    const { value: cfi, index, range } = detail;
    const { booknotes = [] } = getConfig(bookKey)!;
    const annotations = booknotes.filter((booknote) => booknote.type === "annotation" && !booknote.deletedAt);
    const annotation = annotations.find((annotation) => annotation.cfi === cfi);
    if (!annotation) return;
    const selection = { key: bookKey, annotated: true, text: annotation.text ?? "", range, index };
    setSelectedStyle(annotation.style!);
    setSelectedColor(annotation.color!);
    setSelection(selection);
  };

  useFoliateEvents(view, { onLoad, onDrawAnnotation, onShowAnnotation });

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    handleShowPopup(showAnnotPopup || showAskAIPopup);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showAnnotPopup, showAskAIPopup]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    setHighlightOptionsVisible(!!selection?.annotated);
    if (selection && selection.text.trim().length > 0 && !showAskAIPopup) {
      const gridFrame = document.querySelector(`#gridcell-${bookKey}`);
      if (!gridFrame) return;
      const rect = gridFrame.getBoundingClientRect();
      const triangPos = getPosition(selection.range, rect, popupPadding, viewSettings.vertical);
      const annotPopupPos = getPopupPosition(
        triangPos,
        rect,
        viewSettings.vertical ? annotPopupHeight : annotPopupWidth,
        viewSettings.vertical ? annotPopupWidth : annotPopupHeight,
        popupPadding,
      );
      if (triangPos.point.x === 0 || triangPos.point.y === 0) return;
      setAnnotPopupPosition(annotPopupPos);
      setTrianglePosition(triangPos);
      handleShowAnnotPopup();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selection, bookKey, showAskAIPopup]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (!progress) return;
    const { location } = progress;
    const start = CFI.collapse(location);
    const end = CFI.collapse(location, true);
    const { booknotes = [] } = config;
    const annotations = booknotes.filter(
      (item) =>
        !item.deletedAt &&
        item.type === "annotation" &&
        item.style &&
        CFI.compare(item.cfi, start) >= 0 &&
        CFI.compare(item.cfi, end) <= 0,
    );
    try {
      Promise.all(annotations.map((annotation) => view?.addAnnotation(annotation)));
    } catch (e) {
      console.warn(e);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [progress]);

  const handleShowAnnotPopup = () => {
    setShowAnnotPopup(true);
  };

  const handleCopy = () => {
    if (!selection || !selection.text) return;
    if (selection) navigator.clipboard?.writeText(selection.text);
    toast.success("Copy success!");
    handleDismissPopupAndSelection();
  };

  const handleHighlight = (update = false) => {
    if (!selection || !selection.text) return;
    setHighlightOptionsVisible(true);
    const { booknotes: annotations = [] } = config;
    const cfi = view?.getCFI(selection.index, selection.range);
    if (!cfi) return;
    const style = settings.globalReadSettings.highlightStyle;
    const color = settings.globalReadSettings.highlightStyles[style];
    const annotation: BookNote = {
      id: uniqueId(),
      type: "annotation",
      cfi,
      style,
      color,
      text: selection.text,
      note: "",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };
    const existingIndex = annotations.findIndex(
      (annotation) => annotation.cfi === cfi && annotation.type === "annotation" && !annotation.deletedAt,
    );
    const views = getViewsById(bookKey.split("-")[0]!);
    if (existingIndex !== -1) {
      views.forEach((view) => view?.addAnnotation(annotation, true));
      if (update) {
        annotation.id = annotations[existingIndex]!.id;
        annotations[existingIndex] = annotation;
        views.forEach((view) => view?.addAnnotation(annotation));
      } else {
        annotations[existingIndex]!.deletedAt = Date.now();
        setShowAnnotPopup(false);
      }
    } else {
      annotations.push(annotation);
      views.forEach((view) => view?.addAnnotation(annotation));
      setSelection({ ...selection, annotated: true });
    }

    const updatedConfig = updateBooknotes(bookKey, annotations);
    if (updatedConfig) {
      saveConfig(envConfig, bookKey, updatedConfig, settings);
    }
  };

  const handleSearch = () => {
    if (!selection || !selection.text) return;
    setShowAnnotPopup(false);
    eventDispatcher.dispatch("search", { term: selection.text });
  };

  const handleExplain = () => {
    if (!selection || !selection.text) return;
    setShowAnnotPopup(false);
    iframeService.sendExplainTextRequest(selection.text);
  };

  const handleAskAI = useCallback(() => {
    if (!selection || !selection.text) return;

    setShowAnnotPopup(false);
    setShowAskAIPopup(false);

    // Calculate position for AskAI popup similar to highlightOptionsStyle
    const gridFrame = document.querySelector(`#gridcell-${bookKey}`);
    if (!gridFrame) return;
    const rect = gridFrame.getBoundingClientRect();
    const triangPos = getPosition(selection.range, rect, popupPadding, viewSettings.vertical);

    // Calculate AskAI popup position
    const askAIPopupWidth = 320;
    const askAIPopupHeight = 120;
    const askAIPopupPos = getPopupPosition(
      triangPos,
      rect,
      viewSettings.vertical ? askAIPopupHeight : askAIPopupWidth,
      viewSettings.vertical ? askAIPopupWidth : askAIPopupHeight,
      popupPadding,
    );

    if (triangPos.point.x === 0 || triangPos.point.y === 0) return;
    setAskAIPopupPosition(askAIPopupPos);

    // 使用 setTimeout 确保前面的状态更新完成
    setTimeout(() => {
      setShowAskAIPopup(true);
    }, 0);
  }, [selection, bookKey, viewSettings]);

  const handleCloseAskAI = () => {
    setShowAskAIPopup(false);
    view?.deselect();
  };

  const handleSendAIQuery = (query: string, selectedText: string) => {
    // 发送AI查询请求到父窗口
    iframeService.sendAskAIRequest(selectedText, query);
    // toast.success("AI query sent!");
    handleDismissPopupAndSelection();
  };

  const selectionAnnotated = selection?.annotated;
  const buttons = [
    { label: _("Copy"), Icon: FiCopy, onClick: handleCopy },
    { label: _("Explain"), Icon: FiHelpCircle, onClick: handleExplain },
    { label: _("Ask AI"), Icon: FiMessageCircle, onClick: handleAskAI },
    {
      label: undefined,
      Icon: selectionAnnotated ? RiDeleteBinLine : PiHighlighterFill,
      onClick: handleHighlight,
    },
    { label: undefined, Icon: FiSearch, onClick: handleSearch },
  ];

  return (
    <div>
      {showAnnotPopup && !showAskAIPopup && trianglePosition && annotPopupPosition && (
        <AnnotationPopup
          dir={viewSettings.rtl ? "rtl" : "ltr"}
          isVertical={viewSettings.vertical}
          buttons={buttons}
          position={annotPopupPosition}
          trianglePosition={trianglePosition}
          highlightOptionsVisible={highlightOptionsVisible}
          selectedStyle={selectedStyle}
          selectedColor={selectedColor}
          popupWidth={annotPopupWidth}
          popupHeight={annotPopupHeight}
          onHighlight={handleHighlight}
        />
      )}
      {showAskAIPopup && askAIPopupPosition && selection && (
        <AskAIPopup
          style={{
            left: `${askAIPopupPosition.point.x}px`,
            top: `${askAIPopupPosition.point.y}px`,
            width: "320px",
          }}
          selectedText={selection.text}
          onClose={handleCloseAskAI}
          onSendQuery={handleSendAIQuery}
        />
      )}
    </div>
  );
};

export default Annotator;
