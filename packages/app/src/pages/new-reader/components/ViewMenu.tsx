import clsx from "clsx";
import type React from "react";
import { useEffect } from "react";
import { useState } from "react";
import { Bi<PERSON><PERSON>, BiSun } from "react-icons/bi";
import { Md<PERSON><PERSON><PERSON>, MdZoomIn, MdZoomOut } from "react-icons/md";
import { TbSunMoon } from "react-icons/tb";

import MenuItem from "@/components/MenuItem";
import { useEnv } from "@/context/EnvContext";
import { useTranslation } from "@/hooks/useTranslation";
import { MAX_ZOOM_LEVEL, MIN_ZOOM_LEVEL, ZOOM_STEP } from "@/services/constants";
import { useReaderStore } from "@/store/readerStore";
import { useThemeStore } from "@/store/themeStore";
import { getMaxInlineSize } from "@/utils/config";
import { getStyles } from "@/utils/style";
import { saveViewSettings } from "../utils/viewSettingsHelper";

interface ViewMenuProps {
  bookKey: string;
  setIsDropdownOpen?: (open: boolean) => void;
  onSetSettingsDialogOpen: (open: boolean) => void;
}

const ViewMenu: React.FC<ViewMenuProps> = ({ bookKey, setIsDropdownOpen, onSetSettingsDialogOpen }) => {
  const _ = useTranslation();
  const { envConfig, appService } = useEnv();
  const { getView, getViewSettings, setViewSettings } = useReaderStore();
  const viewSettings = getViewSettings(bookKey)!;

  const { themeMode, isDarkMode, setThemeMode } = useThemeStore();
  const [isScrolledMode, setScrolledMode] = useState(viewSettings!.scrolled);
  const [zoomLevel, setZoomLevel] = useState(viewSettings!.zoomLevel!);
  const [invertImgColorInDark, setInvertImgColorInDark] = useState(viewSettings!.invertImgColorInDark);

  const zoomIn = () => setZoomLevel((prev: number) => Math.min(prev + ZOOM_STEP, MAX_ZOOM_LEVEL));
  const zoomOut = () => setZoomLevel((prev: number) => Math.max(prev - ZOOM_STEP, MIN_ZOOM_LEVEL));
  const resetZoom = () => setZoomLevel(100);
  const toggleScrolledMode = () => setScrolledMode(!isScrolledMode);

  const openFontLayoutMenu = () => {
    setIsDropdownOpen?.(false);
    onSetSettingsDialogOpen(true);
  };

  const cycleThemeMode = () => {
    const nextMode = themeMode === "auto" ? "light" : themeMode === "light" ? "dark" : "auto";
    setThemeMode(nextMode);
  };

  const handleFullScreen = () => {
    setIsDropdownOpen?.(false);
  };

  useEffect(() => {
    if (isScrolledMode === viewSettings!.scrolled) return;
    viewSettings!.scrolled = isScrolledMode;
    getView(bookKey)?.renderer.setAttribute("flow", isScrolledMode ? "scrolled" : "paginated");
    getView(bookKey)?.renderer.setAttribute("max-inline-size", `${getMaxInlineSize(viewSettings)}px`);
    getView(bookKey)?.renderer.setStyles?.(getStyles(viewSettings!));
    setViewSettings(bookKey, viewSettings!);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isScrolledMode]);

  useEffect(() => {
    saveViewSettings(envConfig, bookKey, "zoomLevel", zoomLevel, true, true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [zoomLevel]);

  useEffect(() => {
    if (invertImgColorInDark === viewSettings.invertImgColorInDark) return;
    saveViewSettings(envConfig, bookKey, "invertImgColorInDark", invertImgColorInDark, true, true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [invertImgColorInDark]);
  return (
    <div
      tabIndex={0}
      className={clsx(
        "view-menu dropdown-content dropdown-right no-triangle z-20 mt-1 border",
        "bgcolor-base-200 border-base-200 shadow-2xl",
      )}
      style={{
        maxWidth: `${window.innerWidth - 40}px`,
        marginRight: window.innerWidth < 640 ? "-36px" : "0px",
      }}
    >
      <div className={clsx("flex items-center justify-between rounded-md")}>
        <button
          onClick={zoomOut}
          className={clsx(
            "rounded-full p-2 text-base-content hover:bg-base-300",
            zoomLevel <= MIN_ZOOM_LEVEL && "btn-disabled text-gray-400",
          )}
        >
          <MdZoomOut />
        </button>
        <button
          className={clsx("h-8 min-h-8 w-[50%] rounded-md p-1 text-center text-base-content hover:bg-base-300")}
          onClick={resetZoom}
        >
          {zoomLevel}%
        </button>
        <button
          onClick={zoomIn}
          className={clsx(
            "rounded-full p-2 text-base-content hover:bg-base-300",
            zoomLevel >= MAX_ZOOM_LEVEL && "btn-disabled text-gray-400",
          )}
        >
          <MdZoomIn />
        </button>
      </div>

      <hr className="my-1 border-base-300" />

      <MenuItem label={_("Font & Layout")} shortcut="Shift+F" onClick={openFontLayoutMenu} />

      <MenuItem
        label={_("Scrolled Mode")}
        shortcut="Shift+J"
        Icon={isScrolledMode ? MdCheck : undefined}
        onClick={toggleScrolledMode}
      />

      <hr className="my-1 border-base-300" />

      {appService?.hasWindow && <MenuItem label={_("Fullscreen")} onClick={handleFullScreen} />}
      <MenuItem
        label={themeMode === "dark" ? _("Dark Mode") : themeMode === "light" ? _("Light Mode") : _("Auto Mode")}
        Icon={themeMode === "dark" ? BiMoon : themeMode === "light" ? BiSun : TbSunMoon}
        onClick={cycleThemeMode}
      />
      <MenuItem
        label={_("Invert Image In Dark Mode")}
        disabled={!isDarkMode}
        Icon={invertImgColorInDark ? MdCheck : undefined}
        onClick={() => setInvertImgColorInDark(!invertImgColorInDark)}
      />
    </div>
  );
};

export default ViewMenu;
