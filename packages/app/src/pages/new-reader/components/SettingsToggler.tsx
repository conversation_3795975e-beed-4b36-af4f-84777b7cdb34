import Button from "@/components/Button";
import { useBookSettingsStore } from "@/store/bookSettingsStore";
import { useReaderStore } from "@/store/readerStore";
import { Settings2 } from "lucide-react";

const SettingsToggler = () => {
  const { setHoveredBookKey } = useReaderStore();
  const { isFontLayoutSettingsDialogOpen, setFontLayoutSettingsDialogOpen } = useBookSettingsStore();
  const handleToggleSettings = () => {
    setHoveredBookKey("");
    setFontLayoutSettingsDialogOpen(!isFontLayoutSettingsDialogOpen);
  };
  return <Button icon={<Settings2 size={20} />} onClick={handleToggleSettings} />;
};

export default SettingsToggler;
