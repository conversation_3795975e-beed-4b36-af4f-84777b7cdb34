import { atom } from "jotai";

import { type BookDoc, DocumentLoader } from "@/libs/document";
import type { EnvConfigType } from "@/services/environment";
import { formatTitle, getBaseFilename, getPrimaryLanguage } from "@/utils/book";
import { updateToc } from "@/utils/toc";

import type { Book, BookConfig, BookProgress, ViewSettings } from "@/types/book";
import type { FoliateView } from "@/types/view";
import { bookSettingsAtom } from "./bookAtoms";

// Local bookData and viewState management using Jotai for single-book "new-reader"

export interface BookDataAtomState {
  id: string;
  book: Book | null;
  file: File | null;
  config: BookConfig | null;
  bookDoc: BookDoc | null;
}

export const bookDataAtom = atom<BookDataAtomState | null>(null);

// Split state per Jotai philosophy
export const viewAtom = atom<FoliateView | null>(null);
export const progressAtom = atom<BookProgress | null>(null);
export const viewSettingsAtom = atom<ViewSettings | null>(null);

// Backward-compatible combined selector (no loading/error/key)
export const viewStateAtom = atom((get) => ({
  view: get(viewAtom),
  progress: get(progressAtom),
  viewSettings: get(viewSettingsAtom),
}));

// Action atom: initialize bookData and viewState (mirrors readerStore.initViewState for single book)
export const initBookDataActionAtom = atom(
  null,
  async (get, set, params: { envConfig: EnvConfigType; id: string; key?: string }) => {
    const { envConfig, id } = params;
    console.log("Initializing jotai book", id);

    try {
      const settings = get(bookSettingsAtom);
      const appService = await envConfig.getAppService();

      // Find book by id in library
      const { useLibraryStore } = await import("@/store/libraryStore");
      const { library } = useLibraryStore.getState();
      const book = library.find((b) => b.hash === id);
      if (!book) {
        throw new Error("Book not found");
      }

      // Load file via Tauri path to File
      const { convertFileSrc } = await import("@tauri-apps/api/core");
      const fileUrl = convertFileSrc(book.filePath!);
      const response = await fetch(fileUrl);
      const arrayBuffer = await response.arrayBuffer();
      const filename = book.filePath!.split("/").pop() || "book.epub";
      const file = new File([arrayBuffer], filename, { type: "application/epub+zip" });

      // Load or create book config
      const config = await appService.loadBookConfig(book, settings);

      // Open document and enrich metadata
      const { book: bookDoc } = await new DocumentLoader(file).open();
      updateToc(bookDoc, config.viewSettings?.sortedTOC ?? false);
      if (!bookDoc.metadata.title) {
        bookDoc.metadata.title = getBaseFilename(file.name);
      }
      book.sourceTitle = formatTitle(bookDoc.metadata.title);
      const primaryLanguage = getPrimaryLanguage(bookDoc.metadata.language);
      book.primaryLanguage = book.primaryLanguage ?? primaryLanguage;
      book.metadata = book.metadata ?? bookDoc.metadata;

      // Persist into atoms
      set(bookDataAtom, { id, book, file, config, bookDoc });
      set(viewAtom, null);
      set(progressAtom, null);

      const configViewSettings = (config.viewSettings || {}) as ViewSettings;
      const globalViewSettings = settings.globalViewSettings as ViewSettings;
      set(viewSettingsAtom, { ...globalViewSettings, ...configViewSettings });
    } catch (error) {
      console.error(error);
      // reset atoms on failure
      set(bookDataAtom, { id, book: null, file: null, config: null, bookDoc: null });
      set(viewAtom, null);
      set(progressAtom, null);
      set(viewSettingsAtom, null);
    }
  },
);
