import { create } from "zustand";

interface ReaderState {
  count: number;
  increment: () => void;
  decrement: () => void;
}

const createCounterStore = () =>
  create<ReaderState>((set) => ({
    count: 0,
    increment: () => set((state) => ({ count: state.count + 1 })),
    decrement: () => set((state) => ({ count: state.count - 1 })),
  }));

export const useReaderStore = () => createCounterStore();
