import { useChat } from "@/ai/hooks/use-chat";
import { deepseek } from "@/ai/providers/deepseek";
import { ChatMessages } from "@/components/chat/chat-messages";
import ModelSelector from "@/components/chat/model-selector";
import { ChatContainerRoot } from "@/components/prompt-kit/chat-container";
import {
  PromptInput,
  PromptInputAction,
  PromptInputActions,
  PromptInputTextarea,
} from "@/components/prompt-kit/prompt-input";
import { ScrollButton } from "@/components/prompt-kit/scroll-button";
import { Button } from "@/components/ui/button";
import { useModelSelector } from "@/hooks/useModelSelector";
import { useTextEventHandler } from "@/hooks/useTextEvent";
import { createThread, editThread, getLatestThreadByBookKey } from "@/services/threadService";
import { useAppSettingsStore } from "@/store/appSettingsStore";
import { useModelProviderStore } from "@/store/modelProviderStore";
import { useSidebarStore } from "@/store/sidebarStore";
import { useThreadStore } from "@/store/threadStore";
import type { UIMessage } from "ai";
import { ArrowUp, MessageCircle, MessageCirclePlus, Plus, Settings } from "lucide-react";
import { useEffect, useRef, useState } from "react";

function ChatContent() {
  const [input, setInput] = useState("");
  const isInit = useRef(false);
  const { toggleSettingsDialog } = useAppSettingsStore();
  const { sideBarBookKey } = useSidebarStore();
  const { currentThread, setCurrentThread } = useThreadStore();

  const messagesRef = useRef<UIMessage[]>([]);

  const { selectedModel, setSelectedModel, currentModelInstance } = useModelSelector("deepseek", "deepseek-chat");

  const { messages, sendMessage, status, error, stop, setMessages } = useChat(
    currentModelInstance || deepseek("deepseek-chat"),
    {
      experimental_throttle: 50,
      messages: [],
      onToolCall: (toolCall) => {
        console.log("toolCall", toolCall);
      },
      onFinish: ({ message }) => {
        const { currentThread } = useThreadStore.getState();
        const { selectedModel } = useModelProviderStore.getState();
        const messages = messagesRef.current;
        messages[messages.length - 1] = {
          ...message,
          metadata: {
            ...(message.metadata || {}),
            provider: selectedModel,
            selectedModel,
            createdAt: Math.floor(Date.now() / 1000),
            updatedAt: Math.floor(Date.now() / 1000),
          },
        };
        setMessages(messages);
        editThread(currentThread!.id, {
          messages: messages,
        })
          .then((updatedThread) => {
            console.log("Thread updated successfully:", updatedThread.id);
            setCurrentThread(updatedThread);
          })
          .catch((error) => {
            console.error("Failed to update thread:", error);
          });
      },
    },
  );

  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  useEffect(() => {
    const initializeThread = async () => {
      if (sideBarBookKey && !currentThread && !isInit.current) {
        try {
          const latestThread = await getLatestThreadByBookKey(sideBarBookKey);
          if (latestThread) {
            setCurrentThread(latestThread);
            setMessages(latestThread.messages);
            console.log("Loaded existing thread:", latestThread.id);
            isInit.current = true;
          }
        } catch (error) {
          console.error("Failed to load existing thread:", error);
        }
      }
    };

    initializeThread();
  }, [sideBarBookKey, currentThread, setCurrentThread, setMessages]);

  useEffect(() => {
    return () => {
      setCurrentThread(null);
      setMessages([]);
    };
  }, [setCurrentThread, setMessages]);

  useTextEventHandler({
    sendMessage,
  });

  const handleSubmit = async () => {
    if (!input.trim() || status !== "ready") return;
    if (messages.length === 0 && sideBarBookKey && !currentThread) {
      try {
        const thread = await createThread(sideBarBookKey, input.substring(0, 50), []);
        setCurrentThread(thread);
        console.log("Created new thread:", thread.id);
      } catch (error) {
        console.error("Failed to create thread:", error);
      }
    }

    sendMessage({ text: input });
    setInput("");
  };

  const handleNewThread = () => {
    setCurrentThread(null);
    setMessages([]);
  };

  const promptSuggestions = [
    { text: "总结这一页的内容", icon: "📖" },
    { text: "解释这个概念", icon: "💡" },
    { text: "分析作者的观点", icon: "🤔" },
    { text: "找出关键信息", icon: "🔍" },
    { text: "提出相关问题", icon: "❓" },
    { text: "生成学习笔记", icon: "✍️" },
  ];

  const EmptyState = () => (
    <div className="flex h-full w-full select-none flex-col overflow-y-auto p-6">
      <div className="flex flex-1 flex-col items-center justify-center gap-6">
        <div className="flex flex-col items-center gap-3">
          <div className="rounded-full bg-neutral-100 p-3 dark:bg-neutral-800">
            <MessageCircle size={24} className="text-neutral-600 dark:text-neutral-400" />
          </div>
          <div className="text-center">
            <h3 className="font-semibold text-lg text-neutral-900 dark:text-neutral-100">AI 阅读助手</h3>
            <p className="mt-1 max-w-sm text-neutral-600 text-sm dark:text-neutral-400">
              智能分析文本内容，提供深度理解和个性化解答
            </p>
          </div>
        </div>

        <div className="w-full max-w-sm">
          <h4 className="mb-3 font-medium text-neutral-800 text-sm dark:text-neutral-200">常用功能</h4>
          <div className="grid grid-cols-2 gap-2">
            {promptSuggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => setInput(suggestion.text)}
                className="flex flex-col items-center gap-2 rounded-lg border border-neutral-200 bg-white p-3 text-center transition-colors hover:border-neutral-300 hover:bg-neutral-50 dark:border-neutral-700 dark:bg-neutral-800 dark:hover:border-neutral-600 dark:hover:bg-neutral-750"
              >
                <span className="text-lg">{suggestion.icon}</span>
                <span className="text-neutral-700 text-xs leading-tight dark:text-neutral-300">{suggestion.text}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <main className="flex h-full flex-col overflow-hidden ">
      <div className="ml-1 flex-shrink-0 border-neutral-300 dark:border-neutral-700">
        <div className="flex h-8 items-center justify-between">
          <div className="flex flex-1 items-center gap-2 pl-0.5">
            <ModelSelector selectedModel={selectedModel} onModelSelect={setSelectedModel} className="z-40 max-w-80" />
          </div>
          <div className="flex items-center gap-0">
            <Button
              variant="ghost"
              size="icon"
              className="z-40 h-8 w-8 rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-700"
              onClick={handleNewThread}
            >
              <MessageCirclePlus className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="z-40 h-8 w-8 rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-700"
              onClick={toggleSettingsDialog}
            >
              <Settings className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
      {messages.length === 0 && isInit.current ? (
        <EmptyState />
      ) : (
        // Make the messages container a positioning context so the
        // ScrollButton anchors to it instead of the viewport.
        <ChatContainerRoot className="relative flex-1">
          <ChatMessages
            messages={messages}
            status={status}
            error={error}
            onUpdateMessage={(messageId, updates) => {
              const message = messages.find((m) => m.id === messageId);
              if (message?.parts) {
                const updatedParts = message.parts.map((part: any) => {
                  if (part.type === "reasoning") {
                    return { ...part, ...updates };
                  }
                  return part;
                });
                const updatedMessages = messages.map((m) => (m.id === messageId ? { ...m, parts: updatedParts } : m));
                setMessages(updatedMessages);
              }
            }}
          />
          <div className="-translate-x-1/2 absolute bottom-4 left-1/2 flex w-full max-w-3xl justify-end px-5">
            <ScrollButton />
          </div>
        </ChatContainerRoot>
      )}

      <div className="z-10 shrink-0 px-2 pr-0 pb-0 pl-1.5">
        <div className="mx-auto max-w-3xl">
          <PromptInput
            isLoading={status !== "ready"}
            value={input}
            onValueChange={setInput}
            onSubmit={handleSubmit}
            className="relative z-10 w-full rounded-md border p-0 pt-1 shadow-around dark:bg-neutral-800"
          >
            <div className="flex flex-col">
              <PromptInputTextarea
                placeholder="问我任何问题..."
                className="pt-3 pl-4 text-sm leading-[1.3] placeholder:font-light dark:bg-neutral-800 dark:text-neutral-100 dark:placeholder:text-neutral-400"
              />

              <PromptInputActions className="mt-2 flex w-full items-center justify-between gap-2 px-3 pb-3">
                <div className="flex items-center gap-2">
                  <PromptInputAction tooltip="添加附件">
                    <Button
                      variant="outline"
                      size="icon"
                      className="size-7 rounded-full dark:border-neutral-600 dark:hover:bg-neutral-700"
                    >
                      <Plus size={14} />
                    </Button>
                  </PromptInputAction>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    type="submit"
                    size="icon"
                    disabled={status === "ready" ? !input.trim() : status !== "submitted" && status !== "streaming"}
                    onClick={status === "ready" ? handleSubmit : stop}
                    className="size-7 rounded-full"
                  >
                    {status === "ready" ? (
                      <ArrowUp size={18} />
                    ) : (
                      <span className="size-2 rounded-xs bg-white dark:bg-neutral-900" />
                    )}
                  </Button>
                </div>
              </PromptInputActions>
            </div>
          </PromptInput>
        </div>
      </div>
    </main>
  );
}

export default ChatContent;
