import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { type VectorModelConfig, useLlamaStore } from "@/store/llamaStore";
import { Check, Edit2, Plus, Trash2, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

// 模型配置模板
const MODEL_TEMPLATES: Partial<VectorModelConfig>[] = [
  {
    name: "OpenAI Embedding",
    url: "https://api.openai.com/v1/embeddings",
    modelId: "text-embedding-3-small",
    description: "OpenAI 官方嵌入模型",
  },
  {
    name: "Azure OpenAI",
    url: "https://your-resource.openai.azure.com/openai/deployments/your-deployment/embeddings",
    modelId: "text-embedding-ada-002",
    description: "Azure OpenAI 服务",
  },
  {
    name: "本地服务",
    url: "http://localhost:3000/v1/embeddings",
    modelId: "local-embed",
    description: "本地部署的嵌入服务",
  },
];

export default function VectorModelManager() {
  const {
    vectorModelEnabled,
    vectorModels,
    selectedVectorModelId,
    testText,
    setVectorModelEnabled,
    addVectorModel,
    updateVectorModel,
    deleteVectorModel,
    setSelectedVectorModelId,
    getSelectedVectorModel,
  } = useLlamaStore();

  // 状态管理
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [testingId, setTestingId] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<Record<string, string>>({});

  // 表单状态
  const [formData, setFormData] = useState<Omit<VectorModelConfig, "id">>({
    name: "",
    url: "",
    modelId: "",
    apiKey: "",
    description: "",
  });

  const labelClass = "block mb-2 font-medium text-neutral-800 dark:text-neutral-200";

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: "",
      url: "",
      modelId: "",
      apiKey: "",
      description: "",
    });
    setShowAddForm(false);
    setEditingId(null);
  };

  // 使用模板填充表单
  const useTemplate = (template: Partial<VectorModelConfig>) => {
    setFormData({
      name: template.name || "",
      url: template.url || "",
      modelId: template.modelId || "",
      apiKey: "",
      description: template.description || "",
    });
  };

  // 添加新模型
  const handleAdd = () => {
    if (!formData.name.trim() || !formData.url.trim() || !formData.modelId.trim()) {
      toast.error("请填写必填字段：名称、URL、模型ID");
      return;
    }

    const newModel: VectorModelConfig = {
      id: `model-${Date.now()}`,
      ...formData,
    };

    addVectorModel(newModel);
    toast.success(`已添加模型配置：${formData.name}`);
    resetForm();
  };

  // 开始编辑
  const startEdit = (model: VectorModelConfig) => {
    setFormData({
      name: model.name,
      url: model.url,
      modelId: model.modelId,
      apiKey: model.apiKey,
      description: model.description || "",
    });
    setEditingId(model.id);
  };

  // 保存编辑
  const handleEdit = () => {
    if (!editingId || !formData.name.trim() || !formData.url.trim() || !formData.modelId.trim()) {
      toast.error("请填写必填字段");
      return;
    }

    updateVectorModel(editingId, formData);
    toast.success(`已更新模型配置：${formData.name}`);
    resetForm();
  };

  // 删除模型
  const handleDelete = (id: string, name: string) => {
    if (confirm(`确定删除模型配置"${name}"吗？`)) {
      deleteVectorModel(id);
      toast.success(`已删除模型配置：${name}`);
    }
  };

  // 测试连接
  const testModel = async (model: VectorModelConfig) => {
    setTestingId(model.id);
    setTestResults((prev) => ({ ...prev, [model.id]: "测试中..." }));

    try {
      const testUrl = model.url.endsWith("/embeddings") ? model.url : `${model.url.replace(/\/$/, "")}/v1/embeddings`;

      const headers: Record<string, string> = {
        "Content-Type": "application/json",
      };

      if (model.apiKey.trim()) {
        headers.Authorization = `Bearer ${model.apiKey}`;
      }

      const res = await fetch(testUrl, {
        method: "POST",
        headers,
        body: JSON.stringify({
          input: [testText || "测试文本"],
          model: model.modelId,
          encoding_format: "float",
        }),
      });

      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }

      const json = await res.json();
      const len = json?.data?.[0]?.embedding?.length ?? 0;
      setTestResults((prev) => ({ ...prev, [model.id]: `连接成功 | 维度: ${len}` }));
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      setTestResults((prev) => ({ ...prev, [model.id]: `连接失败: ${message}` }));
    } finally {
      setTestingId(null);
    }
  };

  return (
    <section className="rounded-lg bg-neutral-100/90 p-4 dark:bg-neutral-800/90">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="font-semibold text-lg">向量模型配置</h2>
        <div className="flex items-center gap-2">
          {vectorModelEnabled && vectorModels.length > 0 && (
            <span className="rounded-md border border-blue-200 bg-blue-200 px-2 py-1 font-medium text-blue-800 text-xs dark:border-blue-900/40 dark:bg-blue-900/40 dark:text-blue-300">
              {getSelectedVectorModel()?.name || "已启用"}
            </span>
          )}
        </div>
      </div>

      <div className="space-y-6">
        {/* 总开关 */}
        <div className="flex items-center justify-between">
          <div>
            <div className={labelClass}>启用向量模型功能</div>
            <div className="text-neutral-500 text-xs dark:text-neutral-400">
              启用后将使用配置的向量模型，而非本地 Llama.cpp 服务
            </div>
          </div>
          <Switch checked={vectorModelEnabled} onCheckedChange={setVectorModelEnabled} />
        </div>

        {vectorModelEnabled && (
          <>
            {/* 模型列表 */}
            <div className="space-y-3">
              {vectorModels.length === 0 ? (
                <div className="rounded-lg border border-neutral-200 bg-neutral-50 p-4 text-center dark:border-neutral-700 dark:bg-neutral-800">
                  <p className="text-neutral-500 dark:text-neutral-400">暂无配置的模型</p>
                  <p className="text-neutral-500 text-xs dark:text-neutral-400">点击下方"添加模型"开始配置</p>
                </div>
              ) : (
                vectorModels.map((model) => (
                  <div
                    key={model.id}
                    className={`rounded-lg border p-3 ${
                      selectedVectorModelId === model.id
                        ? "border-blue-300 bg-blue-50 dark:border-blue-600 dark:bg-blue-900/20"
                        : "border-neutral-200 bg-white dark:border-neutral-700 dark:bg-neutral-800"
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium text-neutral-900 dark:text-neutral-100">{model.name}</h4>
                          {selectedVectorModelId === model.id && (
                            <span className="rounded-full bg-blue-500 p-1">
                              <Check size={12} className="text-white" />
                            </span>
                          )}
                        </div>
                        <p className="text-neutral-600 text-sm dark:text-neutral-400">
                          {model.modelId} • {model.url}
                        </p>
                        {model.description && (
                          <p className="text-neutral-500 text-xs dark:text-neutral-400">{model.description}</p>
                        )}
                        {testResults[model.id] && (
                          <p
                            className={`mt-1 text-xs ${
                              testResults[model.id].includes("成功")
                                ? "text-green-600 dark:text-green-400"
                                : testResults[model.id].includes("失败")
                                  ? "text-red-600 dark:text-red-400"
                                  : "text-neutral-600 dark:text-neutral-400"
                            }`}
                          >
                            {testResults[model.id]}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-1">
                        {selectedVectorModelId !== model.id && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setSelectedVectorModelId(model.id)}
                            className="h-8 px-2"
                          >
                            选择
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => testModel(model)}
                          disabled={testingId === model.id}
                          className="h-8 px-2"
                        >
                          {testingId === model.id ? "测试中" : "测试"}
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => startEdit(model)} className="h-8 px-2">
                          <Edit2 size={14} />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDelete(model.id, model.name)}
                          className="h-8 px-2 text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
                        >
                          <Trash2 size={14} />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* 添加/编辑表单 */}
            {(showAddForm || editingId) && (
              <div className="rounded-lg border border-neutral-200 bg-white p-4 dark:border-neutral-700 dark:bg-neutral-800">
                <div className="mb-3 flex items-center justify-between">
                  <h4 className="font-medium text-neutral-900 dark:text-neutral-100">
                    {editingId ? "编辑模型配置" : "添加新模型"}
                  </h4>
                  <Button size="sm" variant="ghost" onClick={resetForm} className="h-8 px-2">
                    <X size={14} />
                  </Button>
                </div>

                {/* 快速模板 */}
                {!editingId && (
                  <div className="mb-4">
                    <div className="mb-2 text-neutral-700 text-sm dark:text-neutral-300">快速模板：</div>
                    <div className="flex flex-wrap gap-2">
                      {MODEL_TEMPLATES.map((template, index) => (
                        <Button
                          key={index}
                          size="sm"
                          variant="outline"
                          onClick={() => useTemplate(template)}
                          className="h-8 text-xs"
                        >
                          {template.name}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}

                <div className="grid gap-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className={labelClass}>名称 *</label>
                      <Input
                        value={formData.name}
                        onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                        placeholder="OpenAI Embedding"
                        className="h-8"
                      />
                    </div>
                    <div>
                      <label className={labelClass}>模型ID *</label>
                      <Input
                        value={formData.modelId}
                        onChange={(e) => setFormData((prev) => ({ ...prev, modelId: e.target.value }))}
                        placeholder="text-embedding-3-small"
                        className="h-8"
                      />
                    </div>
                  </div>
                  <div>
                    <label className={labelClass}>API端点 *</label>
                    <Input
                      value={formData.url}
                      onChange={(e) => setFormData((prev) => ({ ...prev, url: e.target.value }))}
                      placeholder="https://api.openai.com/v1/embeddings"
                      className="h-8"
                    />
                  </div>
                  <div>
                    <label className={labelClass}>API Key</label>
                    <Input
                      type="password"
                      value={formData.apiKey}
                      onChange={(e) => setFormData((prev) => ({ ...prev, apiKey: e.target.value }))}
                      placeholder="sk-..."
                      className="h-8"
                    />
                  </div>
                  <div>
                    <label className={labelClass}>描述</label>
                    <Input
                      value={formData.description}
                      onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                      placeholder="模型描述信息"
                      className="h-8"
                    />
                  </div>
                </div>

                <div className="mt-4 flex justify-end gap-2">
                  <Button size="sm" variant="outline" onClick={resetForm}>
                    取消
                  </Button>
                  <Button size="sm" onClick={editingId ? handleEdit : handleAdd}>
                    {editingId ? "保存" : "添加"}
                  </Button>
                </div>
              </div>
            )}

            {/* 添加按钮 */}
            {!showAddForm && !editingId && (
              <Button variant="outline" onClick={() => setShowAddForm(true)} className="flex w-full items-center gap-2">
                <Plus size={16} />
                添加模型配置
              </Button>
            )}
          </>
        )}
      </div>
    </section>
  );
}
