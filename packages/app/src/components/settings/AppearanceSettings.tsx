import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useThemeStore } from "@/store/themeStore";
import type { ThemeMode } from "@/styles/themes";
import clsx from "clsx";
import { ChevronDownIcon } from "lucide-react";
import { useEffect } from "react";

export default function AppearanceSettings() {
  const { themeMode, setThemeMode, isDarkMode } = useThemeStore();

  const themeOptions = [
    { value: "auto" as ThemeMode, label: "System" },
    { value: "light" as ThemeMode, label: "Light" },
    { value: "dark" as ThemeMode, label: "Dark" },
  ];

  const handleThemeChange = (mode: ThemeMode) => {
    setThemeMode(mode);
  };

  const getCurrentThemeLabel = () => {
    return themeOptions.find((option) => option.value === themeMode)?.label || "System";
  };

  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [isDarkMode]);

  return (
    <div className="space-y-8 p-4">
      <section className="rounded-lg bg-neutral-100/90 p-4 dark:bg-neutral-800/90">
        <h2 className="text mb-4 dark:text-neutral-200">外观</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <span className="text dark:text-neutral-200">主题</span>
              <p className="mt-2 text-neutral-600 text-xs dark:text-neutral-400">选择您偏好的主题</p>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="outline" className="w-32 justify-between">
                  {getCurrentThemeLabel()}
                  <ChevronDownIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-32">
                {themeOptions.map((option) => (
                  <DropdownMenuItem
                    key={option.value}
                    onClick={() => handleThemeChange(option.value)}
                    className={clsx("my-0.5", themeMode === option.value ? "bg-accent" : "")}
                  >
                    {option.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </section>
    </div>
  );
}
