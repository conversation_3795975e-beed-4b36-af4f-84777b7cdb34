import ChatContent from "@/components/chat/chat-sidebar";
import SettingsDialog from "@/components/settings/SettingsDialog";
import { Button } from "@/components/ui/button";
import { Tabs } from "app-tabs";
import { Resizable } from "re-resizable";

import { LlamaServerManager, LlamacppClient } from "@/components/settings/LlamaClient";
import { useTabsContext } from "@/context/TabsContext";
import LibraryPage from "@/pages/library/index";
import NewReaderPage from "@/pages/new-reader";
import { useActiveBookStore } from "@/store/activeBookStore";
import { useAppSettingsStore } from "@/store/appSettingsStore";
import { useLlamaStore } from "@/store/llamaStore";
import { useReaderStore } from "@/store/readerStore";
import { useSidebarStore } from "@/store/sidebarStore";
import { useThemeStore } from "@/store/themeStore";
import { HomeIcon, MessageCircleMore } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";

const Layout = () => {
  const { activeBookMetadata } = useActiveBookStore();
  const { isSettingsDialogOpen, toggleSettingsDialog } = useAppSettingsStore();
  const { tabs, activateTab, removeTab, activeTabId, isLibraryActive, navigateToLibrary } = useTabsContext();
  const { setSideBarBookKey } = useSidebarStore();
  const { isDarkMode } = useThemeStore();
  const { viewStates } = useReaderStore();
  const [isChatVisible, setIsChatVisible] = useState(true);
  const [showOverlay, setShowOverlay] = useState(false);
  const [shouldRenderReader, setShouldRenderReader] = useState(false);
  const lastActiveTabId = useRef<string | null>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const isAnyTabLoading = Object.values(viewStates).some((viewState) => viewState.loading);

  // Auto-start Llama embedding server on app startup and store the session
  const { currentSession, setCurrentSession, setServerStatus, modelPath, vectorModelEnabled } = useLlamaStore();
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  // useEffect(() => {
  //   let cancelled = false;
  //   (async () => {
  //     console.log("vectorModelEnabled", vectorModelEnabled);
  //     try {
  //       if (vectorModelEnabled || currentSession) return;
  //       // Try to detect existing sessions
  //       const client = new LlamacppClient();
  //       const sessions = await client.getAllSessions();
  //       if (cancelled) return;
  //       if (sessions && sessions.length > 0) {
  //         setCurrentSession(sessions[0]);
  //         setServerStatus(`检测到已运行的服务器 | PID: ${sessions[0].pid} | Port: ${sessions[0].port}`);
  //         return;
  //       }
  //       // Start a new server
  //       setServerStatus("启动 Embedding 服务器…");
  //       const serverManager = new LlamaServerManager();
  //       const session = await serverManager.startEmbeddingServer(modelPath);
  //       if (cancelled) return;
  //       setCurrentSession(session);
  //       setServerStatus(`服务器启动成功 | PID: ${session.pid} | Port: ${session.port}`);
  //     } catch (error) {
  //       if (cancelled) return;
  //       const msg = error instanceof Error ? error.message : String(error);
  //       setServerStatus(`自动启动失败：${msg}`);
  //       console.error("自动启动 Llama 服务器失败:", error);
  //     }
  //   })();
  //   return () => {
  //     cancelled = true;
  //   };
  // }, []);

  useEffect(() => {
    if (activeTabId !== lastActiveTabId.current) {
      lastActiveTabId.current = activeTabId;

      if (activeTabId?.startsWith("reader-")) {
        setShouldRenderReader(false);
        setSideBarBookKey(activeTabId.replace("reader-", ""));
        const timer = setTimeout(() => {
          setShouldRenderReader(true);
        }, 100);

        return () => clearTimeout(timer);
      }
      setShouldRenderReader(false);
    }
  }, [activeTabId, setSideBarBookKey]);

  useEffect(() => {
    const handleResize = () => {
      setShowOverlay(true);

      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      resizeTimeoutRef.current = setTimeout(() => {
        setShowOverlay(false);
      }, 200);
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, []);

  const handleChatToggle = useCallback(() => {
    setShowOverlay(true);
    setIsChatVisible(!isChatVisible);

    setTimeout(() => {
      setShowOverlay(false);
    }, 200);
  }, [isChatVisible]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const isCloseShortcut =
        (event.metaKey && event.key === "w" && event.code === "KeyW") || // cmd+w on macOS
        (event.ctrlKey && event.key === "w" && event.code === "KeyW"); // ctrl+w on Windows/Linux

      if (isCloseShortcut) {
        event.preventDefault();
        if (activeTabId && activeTabId !== "library") {
          removeTab(activeTabId);
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [activeTabId, removeTab]);

  const handleTabReorder = useCallback((tabId: string, fromIndex: number, toIndex: number) => {
    console.log("Tab reorder:", tabId, fromIndex, toIndex);
  }, []);

  const renderTabContent = useCallback(() => {
    return (
      <div className="relative h-full">
        {(() => {
          const activeTab = tabs.find((tab) => tab.active);

          if (!activeTab || activeTab.id === "library") {
            return <LibraryPage />;
          }

          if (activeTab.id.startsWith("reader-")) {
            const bookIds = activeTab.id.replace("reader-", "");

            return (
              <div className="flex h-[calc(100vh-40px)] flex-1 bg-background p-1">
                <div className="relative flex-1 rounded-md border shadow-around">
                  {shouldRenderReader ? (
                    <NewReaderPage key={activeTab.id} id={bookIds} />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center">
                      <div className="h-6 w-6 animate-spin rounded-full border-2 border-neutral-300 border-t-neutral-700 dark:border-neutral-600 dark:border-t-neutral-400" />
                    </div>
                  )}

                  {(showOverlay || isAnyTabLoading) && (
                    <div className="absolute inset-0 z-50 flex items-center justify-center rounded-md bg-white/70 backdrop-blur-sm dark:bg-neutral-900/60" />
                  )}
                </div>
                {isChatVisible && (
                  <Resizable
                    defaultSize={{
                      width: 380,
                      height: "100%",
                    }}
                    minWidth={350}
                    maxWidth={580}
                    enable={{
                      top: false,
                      right: false,
                      bottom: false,
                      left: true,
                      topRight: false,
                      bottomRight: false,
                      bottomLeft: false,
                      topLeft: false,
                    }}
                    handleComponent={{
                      left: <div className="custom-resize-handle" />,
                    }}
                    className="h-full"
                    onResize={() => {
                      if (!showOverlay) {
                        setShowOverlay(true);
                      }
                    }}
                    onResizeStop={() => {
                      setShowOverlay(false);
                      window.dispatchEvent(
                        new CustomEvent("foliate-resize-update", {
                          detail: { bookIds },
                        }),
                      );
                    }}
                  >
                    <div className="m-1 mt-0 h-[calc(100dvh-48px)] rounded-md">
                      {activeBookMetadata ? (
                        <ChatContent key={`chat-${activeTab.id}`} />
                      ) : (
                        <div className="flex h-full items-center justify-center text-neutral-500">
                          <div className="text-center">
                            <div className="font-medium text-lg">加载中...</div>
                            <div className="text-sm">正在加载书籍信息</div>
                          </div>
                        </div>
                      )}
                    </div>
                  </Resizable>
                )}
              </div>
            );
          }

          return <LibraryPage />;
        })()}
      </div>
    );
  }, [tabs, isChatVisible, showOverlay, shouldRenderReader, isAnyTabLoading, activeBookMetadata]);

  return (
    <div className="data-tauri-drag-region app-background flex h-screen">
      <div className="flex min-w-0 flex-1 flex-col">
        <div className="border-neutral-200 dark:border-neutral-700 dark:bg-tab-background">
          <Tabs
            tabs={tabs}
            onTabActive={activateTab}
            onTabClose={removeTab}
            onTabReorder={handleTabReorder}
            draggable={true}
            darkMode={isDarkMode}
            className="min-h-8"
            pinnedLeft={
              <div className="mx-2 mt-1 flex items-center gap-2" onClick={navigateToLibrary}>
                <HomeIcon className="size-5 text-neutral-700 hover:text-neutral-800 dark:text-neutral-400 dark:hover:text-neutral-200" />
              </div>
            }
            pinnedRight={
              !isLibraryActive && (
                <div className="ml-8 flex items-center gap-2">
                  <Button onClick={handleChatToggle} className="h-7 rounded-md">
                    <MessageCircleMore className="h-5 w-5" />
                    Chat
                  </Button>
                </div>
              )
            }
          />
        </div>

        <main className="flex-1 overflow-hidden rounded-md">{renderTabContent()}</main>
      </div>

      <SettingsDialog open={isSettingsDialogOpen} onOpenChange={toggleSettingsDialog} />
    </div>
  );
};

export default Layout;
