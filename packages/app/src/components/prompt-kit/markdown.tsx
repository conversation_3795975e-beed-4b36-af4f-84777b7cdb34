import { cn } from "@/lib/utils";
import { useActiveBookStore } from "@/store/activeBookStore";
import { convertFileSrc } from "@tauri-apps/api/core";
import { marked } from "marked";
import { memo, useId, useMemo } from "react";
import ReactMarkdown, { type Components } from "react-markdown";
import remarkBreaks from "remark-breaks";
import remarkCjkFriendly from "remark-cjk-friendly";
import remarkGfm from "remark-gfm";
import { CodeBlock, CodeBlockCode } from "./code-block";

export type MarkdownProps = {
  children: string;
  id?: string;
  className?: string;
  components?: Partial<Components>;
};

function parseMarkdownIntoBlocks(markdown: string): string[] {
  const tokens = marked.lexer(markdown);
  return tokens.map((token) => token.raw);
}

function extractLanguage(className?: string): string {
  if (!className) return "plaintext";
  const match = className.match(/language-(\w+)/);
  return match ? match[1] : "plaintext";
}

// 检测是否为相对路径
function isRelativePath(src: string): boolean {
  return src.startsWith("../") || src.startsWith("./");
}

// 解析相对路径，拼接 baseDir
function resolveImagePath(src: string, baseDir?: string): string {
  if (!isRelativePath(src) || !baseDir) {
    return src; // 不是相对路径或没有 baseDir，返回原始路径
  }

  // 处理 ../ 开头的路径（最常见的情况）
  if (src.startsWith("../")) {
    // 假设 markdown 文件在 baseDir 的子目录中
    // ../images/xxx.jpg 应该解析为 baseDir/images/xxx.jpg
    const pathAfterDotDot = src.substring(3); // 去掉 "../"
    return `${baseDir}/${pathAfterDotDot}`;
  }

  // 处理 ./ 开头的路径
  if (src.startsWith("./")) {
    const pathAfterDot = src.substring(2); // 去掉 "./"
    return `${baseDir}/${pathAfterDot}`;
  }

  // 其他情况，直接拼接
  return `${baseDir}/${src}`;
}

const INITIAL_COMPONENTS: Partial<Components> = {
  code: function CodeComponent({ className, children, ...props }) {
    const isInline =
      !props.node?.position?.start.line || props.node?.position?.start.line === props.node?.position?.end.line;

    if (isInline) {
      return (
        <span className={cn("rounded-sm bg-primary-foreground px-1 font-mono text-sm", className)} {...props}>
          {children}
        </span>
      );
    }

    const language = extractLanguage(className);

    return (
      <CodeBlock className={className}>
        <CodeBlockCode code={children as string} language={language} />
      </CodeBlock>
    );
  },
  pre: function PreComponent({ children }) {
    return <>{children}</>;
  },
};

const MemoizedMarkdownBlock = memo(
  function MarkdownBlock({
    content,
    components = INITIAL_COMPONENTS,
  }: {
    content: string;
    components?: Partial<Components>;
  }) {
    return (
      <ReactMarkdown remarkPlugins={[remarkGfm, remarkBreaks, remarkCjkFriendly]} components={components}>
        {content}
      </ReactMarkdown>
    );
  },
  function propsAreEqual(prevProps, nextProps) {
    return prevProps.content === nextProps.content;
  },
);

MemoizedMarkdownBlock.displayName = "MemoizedMarkdownBlock";

function MarkdownComponent({ children, id, className, components }: MarkdownProps) {
  const generatedId = useId();
  const blockId = id ?? generatedId;
  const blocks = useMemo(() => parseMarkdownIntoBlocks(children), [children]);
  const { activeBookMetadata } = useActiveBookStore();

  // 创建包含自定义 img 组件的 components 对象
  const finalComponents = useMemo(() => {
    const imgComponent = function ImgComponent({
      src,
      alt,
      ...props
    }: { src?: string; alt?: string; [key: string]: any }) {
      if (!src) {
        return <img alt={alt} {...props} />;
      }

      let resolvedSrc = src;

      if (isRelativePath(src) && activeBookMetadata?.base_dir) {
        try {
          const fullPath = resolveImagePath(src, activeBookMetadata.base_dir);
          resolvedSrc = convertFileSrc(fullPath);
        } catch (error) {
          console.warn(`Failed to resolve image path: ${src}`, error);
          resolvedSrc = src;
        }
      } else {
        console.warn("Image path not resolved:", {
          reason: !isRelativePath(src) ? "Not relative path" : "No base_dir available",
        });
      }

      return <img src={resolvedSrc} alt={alt} {...props} />;
    };

    return {
      ...INITIAL_COMPONENTS,
      img: imgComponent,
      ...components, // 允许外部传入的 components 覆盖
    };
  }, [activeBookMetadata, components]);

  return (
    <div className={className}>
      {blocks.map((block, index) => (
        <MemoizedMarkdownBlock key={`${blockId}-block-${index}`} content={block} components={finalComponents} />
      ))}
    </div>
  );
}

const Markdown = memo(MarkdownComponent);
Markdown.displayName = "Markdown";

export { Markdown };
