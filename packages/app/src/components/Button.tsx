import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useEnv } from "@/context/EnvContext";
import clsx from "clsx";
import type React from "react";

interface ButtonProps {
  icon: React.ReactNode;
  onClick: () => void;
  disabled?: boolean;
  tooltip?: string;
  tooltipDirection?: "top" | "bottom" | "left" | "right";
  className?: string;
}

const Button: React.FC<ButtonProps> = ({
  icon,
  onClick,
  disabled = false,
  tooltip,
  tooltipDirection = "top",
  className,
}) => {
  const { appService } = useEnv();

  const button = (
    <button
      className={clsx(
        "btn btn-ghost h-8 min-h-8 w-8 rounded-full p-0",
        appService?.isMobileApp && "hover:bg-transparent",
        disabled && "btn-disabled !bg-transparent opacity-50",
        className,
      )}
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
    >
      {icon}
    </button>
  );

  if (!tooltip) {
    return button;
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>{button}</TooltipTrigger>
      <TooltipContent side={tooltipDirection}>
        <p>{tooltip}</p>
      </TooltipContent>
    </Tooltip>
  );
};

export default Button;
