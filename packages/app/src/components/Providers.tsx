import { IconContext } from "react-icons";
import { EnvProvider } from "@/context/EnvContext";
import { useDefaultIconSize } from "@/hooks/useResponsiveSize";

const Providers = ({ children }: { children: React.ReactNode }) => {
  const iconSize = useDefaultIconSize();
  return (
    <EnvProvider>
      <IconContext.Provider value={{ size: `${iconSize}px` }}>{children}</IconContext.Provider>
    </EnvProvider>
  );
};

export default Providers;
