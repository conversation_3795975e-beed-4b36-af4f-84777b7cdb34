import { useEffect, useRef, useState } from "react";

export function useReasoningTimer(
  reasoningActive: boolean,
  hasReasoning: boolean,
  messageId: string,
  onUpdateMessage?: (messageId: string, updates: any) => void,
) {
  const [elapsedTime, setElapsedTime] = useState(0);
  const startTimeRef = useRef<number | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const hasUpdatedRef = useRef(false);
  const currentMessageIdRef = useRef<string>("");
  const onUpdateMessageRef = useRef(onUpdateMessage);

  // 更新 ref
  useEffect(() => {
    onUpdateMessageRef.current = onUpdateMessage;
  });

  // 当开始新的推理时，重置状态
  useEffect(() => {
    if (reasoningActive && hasReasoning && messageId !== currentMessageIdRef.current) {
      // 清理之前的计时器
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      // 重置状态
      startTimeRef.current = Date.now();
      setElapsedTime(0);
      hasUpdatedRef.current = false;
      currentMessageIdRef.current = messageId;
    }
  }, [reasoningActive, hasReasoning, messageId]);

  // 管理计时器的启动和停止
  useEffect(() => {
    if (reasoningActive && hasReasoning && startTimeRef.current && !intervalRef.current) {
      intervalRef.current = setInterval(() => {
        if (startTimeRef.current) {
          const elapsed = Math.floor((Date.now() - startTimeRef.current) / 1000);
          setElapsedTime(elapsed);
        }
      }, 1000);
    } else if (
      !reasoningActive &&
      startTimeRef.current &&
      !hasUpdatedRef.current &&
      onUpdateMessageRef.current &&
      currentMessageIdRef.current
    ) {
      const finalTime = Math.floor((Date.now() - startTimeRef.current) / 1000);
      onUpdateMessageRef.current(currentMessageIdRef.current, { thinkingTime: finalTime });
      hasUpdatedRef.current = true;
      startTimeRef.current = null;
      currentMessageIdRef.current = "";

      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [reasoningActive, hasReasoning]);

  return { elapsedTime };
}
