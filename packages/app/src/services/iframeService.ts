/**
 * 文本解释服务
 * 使用自定义事件在同一页面内传递选中的文本
 */

export interface ExplainTextEventDetail {
  text: string;
  timestamp: number;
}

export interface ExplainTextEvent extends CustomEvent<ExplainTextEventDetail> {
  type: "explainText";
}

class IframeService {
  private static instance: IframeService;

  private constructor() {
    // 不再需要 postMessage 监听器
  }

  public static getInstance(): IframeService {
    if (!IframeService.instance) {
      IframeService.instance = new IframeService();
    }
    return IframeService.instance;
  }

  /**
   * 发送解释文本请求
   * @param text 要解释的文本
   */
  public sendExplainTextRequest(text: string, type: "explain" | "ask" = "explain"): void {
    if (!text || text.trim().length === 0) {
      console.warn("⚠️ 尝试发送空文本");
      return;
    }

    const eventDetail: ExplainTextEventDetail = {
      text: type === "explain" ? `请解释这段文字:「${text}」` : text,
      timestamp: Date.now(),
    };

    console.log("📤 发送解释文本请求:", eventDetail);

    // 派发自定义事件
    const event = new CustomEvent<ExplainTextEventDetail>("explainText", {
      detail: eventDetail,
      bubbles: true,
      cancelable: true,
    });

    window.dispatchEvent(event);
    console.log("✅ 自定义事件已派发");
  }

  /**
   * 发送 AI 问答请求
   * @param selectedText 选中的文本
   * @param question 用户的问题
   */
  public sendAskAIRequest(selectedText: string, question: string): void {
    if (!selectedText || selectedText.trim().length === 0) {
      console.warn("⚠️ 尝试发送空的选中文本");
      return;
    }

    if (!question || question.trim().length === 0) {
      console.warn("⚠️ 尝试发送空问题");
      return;
    }

    // 将选中文本和问题拼接成一个完整的文本
    const combinedText = `「${selectedText.trim()}」${question.trim()}`;

    // 直接使用 sendExplainTextRequest 发送拼接后的文本
    this.sendExplainTextRequest(combinedText, "ask");
  }

  /**
   * 销毁服务（现在不需要清理任何监听器）
   */
  public destroy(): void {
    // 不再需要清理 postMessage 监听器
    console.log("🧹 IframeService 已销毁");
  }
}

// 导出单例实例
export const iframeService = IframeService.getInstance();

// 导出类以便测试
export { IframeService };
