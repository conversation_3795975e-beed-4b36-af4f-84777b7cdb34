import type { AppService } from "@/types/system";
import { READEST_WEB_BASE_URL } from "./constants";

declare global {
  interface Window {
    __READEST_CLI_ACCESS?: boolean;
  }
}

export const isWebAppPlatform = () => true;
export const hasCli = () => window.__READEST_CLI_ACCESS === true;
export const isPWA = () => window.matchMedia("(display-mode: standalone)").matches;
export const getBaseUrl = () => process.env.NEXT_PUBLIC_API_BASE_URL ?? READEST_WEB_BASE_URL;

// Dev API only in development mode and web platform
// with command `pnpm dev-web`
// for production build or tauri app use the production Web API
export const getAPIBaseUrl = () => (import.meta.env.DEV && isWebAppPlatform() ? "/api" : `${getBaseUrl()}/api`);

export interface EnvConfigType {
  getAppService: () => Promise<AppService>;
}

let webAppService: AppService | null = null;
const getWebAppService = async () => {
  if (!webAppService) {
    const { WebAppService } = await import("@/services/webAppService");
    webAppService = new WebAppService();
    await webAppService.loadSettings();
  }
  return webAppService;
};

const environmentConfig: EnvConfigType = {
  getAppService: async () => {
    return getWebAppService();
  },
};

export default environmentConfig;
