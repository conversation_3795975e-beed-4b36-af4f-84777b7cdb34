import type { RawThread, Thread } from "@/types/thread";
import { invoke } from "@tauri-apps/api/core";
import type { UIMessage } from "ai";

export async function createThread(bookKey: string, title: string, initialMessages: UIMessage[]): Promise<Thread> {
  try {
    const payload = {
      book_key: bookKey,
      title,
      metadata: JSON.stringify({}),
      messages_json: JSON.stringify(initialMessages),
    };

    const newThread: RawThread = await invoke("create_thread", { payload });

    const thread: Thread = {
      ...newThread,
      messages: JSON.parse(newThread.messages),
    };

    return thread;
  } catch (error) {
    console.error("Error creating thread:", error);
    throw new Error("Failed to create thread on the backend.");
  }
}

export interface EditThreadOptions {
  title?: string;
  metadata?: Record<string, any>;
  messages?: UIMessage[];
}

export async function editThread(threadId: string, options: EditThreadOptions): Promise<Thread> {
  try {
    const payload = {
      id: threadId,
      title: options.title,
      metadata: options.metadata ? JSON.stringify(options.metadata) : undefined,
      messages_json: options.messages ? JSON.stringify(options.messages) : undefined,
    };

    const updatedThread: RawThread = await invoke("edit_thread", { payload });

    const thread: Thread = {
      ...updatedThread,
      messages: JSON.parse(updatedThread.messages),
    };

    return thread;
  } catch (error) {
    console.error("Error editing thread:", error);
    throw new Error("Failed to edit thread on the backend.");
  }
}

export async function getLatestThreadByBookKey(bookKey: string): Promise<Thread | null> {
  try {
    console.log("getLatestThreadByBookKey", bookKey);
    const result: RawThread | null = await invoke("get_latest_thread_by_book_key", { bookKey });

    if (result) {
      const thread: Thread = {
        ...result,
        messages: JSON.parse(result.messages),
      };
      return thread;
    }

    return null;
  } catch (error) {
    console.error("Error getting latest thread by book key:", error);
    throw new Error("Failed to get latest thread from the backend.");
  }
}
