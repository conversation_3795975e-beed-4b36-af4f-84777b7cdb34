import { useActiveBookStore } from "@/store/activeBookStore";
import { useLlamaStore } from "@/store/llamaStore";
import { invoke } from "@tauri-apps/api/core";
import { tool } from "ai";
import { z } from "zod";

// 获取向量模型配置的工具函数
function getVectorModelConfig() {
  const llamaState = useLlamaStore.getState();
  // 如果启用了向量模型功能且有选中的模型，优先使用
  if (llamaState.vectorModelEnabled) {
    const selectedModel = llamaState.getSelectedVectorModel();
    if (selectedModel) {
      return {
        baseUrl: selectedModel.url,
        model: selectedModel.modelId,
        apiKey: selectedModel.apiKey || null,
        dimension: 1024, // 保持默认维度
        source: "external",
      };
    }
  }

  // 回退到本地 llama.cpp 服务
  const port = llamaState.currentSession?.port;
  const baseUrl = port ? `http://127.0.0.1:${port}` : "http://127.0.0.1:3544";

  return {
    baseUrl,
    model: "local-embed",
    apiKey: null,
    dimension: 1024,
    source: "local",
  };
}

// 增强的搜索结果类型，包含位置信息
type EnhancedSearchItem = {
  book_title: string;
  book_author: string;
  related_chapter_titles: string;
  content: string;
  similarity: number;

  // 位置信息，用于智能上下文检索
  chunk_id: number | null;
  md_file_path: string;
  file_order_in_book: number;
  global_chunk_index: number;
  chunk_order_in_file: number;
  total_chunks_in_file: number;
};

// 智能RAG搜索工具：混合检索系统（BM25 + 向量检索）+ 精确定位 + 智能权重
export const ragSearchTool = tool({
  description: `在当前图书中执行智能混合检索，结合关键词匹配和语义理解，返回最相关的内容片段。

🔍 **搜索模式**：
• vector：纯向量语义搜索，适合概念性查询和同义词匹配
• bm25：纯文本关键词搜索，适合精确词汇匹配和专业术语
• hybrid：智能混合搜索（默认），自动平衡语义理解和关键词匹配

🧠 **智能特性**：
• 自动权重调整：根据查询长度和复杂度优化搜索策略
• 精确定位：返回章节、页面、段落等详细位置信息
• 上下文感知：支持后续的智能上下文扩展检索

💡 **使用建议**：
• 短查询（1-2词）：系统自动偏重关键词匹配
• 长查询（复杂问题）：系统自动偏重语义理解
• 专业术语：建议使用bm25模式获得精确匹配
• 概念理解：建议使用vector模式获得语义相关内容`,
  inputSchema: z.object({
    reasoning: z.string().min(1).describe("调用此工具的原因和目的，例如：'用户询问关于机器学习的问题，需要搜索相关技术内容'"),
    question: z.string().min(1).describe("用户的查询问题，支持自然语言表达，系统将自动选择最佳搜索策略"),
    limit: z.number().int().min(1).max(20).default(3).describe("返回的内容片段数量，建议3-5个获得平衡的信息覆盖"),
    format: z.boolean().default(true).describe("是否返回格式化的上下文文本，包含搜索统计和位置信息"),

    // 高级搜索选项
    searchMode: z.enum(["vector", "bm25", "hybrid"]).default("hybrid").describe(`搜索模式选择：
• hybrid（推荐）：智能混合搜索，自动平衡关键词和语义匹配
• vector：纯语义搜索，适合概念性问题和同义词查找
• bm25：纯关键词搜索，适合专业术语和精确词汇匹配`),
    vectorWeight: z.number().min(0).max(1).default(0.7).describe("向量搜索权重（0-1），仅hybrid模式生效。0.8+适合概念查询，0.5-0.7适合平衡查询"),
    bm25Weight: z.number().min(0).max(1).default(0.3).describe("关键词搜索权重（0-1），仅hybrid模式生效。0.5+适合术语查询，0.2-0.4适合概念查询"),
  }),
  execute: async ({
    reasoning,
    question,
    limit,
    format,
    searchMode,
    vectorWeight,
    bm25Weight,
  }: {
    reasoning: string;
    question: string;
    limit?: number;
    format?: boolean;
    searchMode?: "vector" | "bm25" | "hybrid";
    vectorWeight?: number;
    bm25Weight?: number;
  }) => {
    const { activeBookId } = useActiveBookStore.getState();
    if (!activeBookId) {
      throw new Error("未找到当前阅读图书，请先在阅读器中打开图书");
    }

    console.log(`执行ragSearchTool - 模式: ${searchMode ?? "hybrid"}, 向量权重: ${vectorWeight ?? 0.7}, BM25权重: ${bm25Weight ?? 0.3}`);

    // 获取向量模型配置（优先使用外部配置，回退到本地服务）
    const vectorConfig = getVectorModelConfig();

    const results = (await invoke("plugin:epub|search_db", {
      bookId: activeBookId,
      query: question,
      limit: limit ?? 5,
      dimension: vectorConfig.dimension,
      baseUrl: vectorConfig.baseUrl,
      model: vectorConfig.model,
      apiKey: vectorConfig.apiKey,
      // 新增混合搜索参数
      searchMode: searchMode ?? "hybrid",
      vectorWeight: vectorWeight ?? 0.7,
      bm25Weight: bm25Weight ?? 0.3,
    })) as EnhancedSearchItem[];

    // 结构化结果，包含完整位置信息
    const enhancedContext = results.map((r, idx) => ({
      // 基础信息
      rank: idx + 1,
      related_chapter_titles: r.related_chapter_titles,
      similarity: Number.parseFloat((r.similarity * 100).toFixed(1)),
      content: r.content,

      // 关键位置信息，用于后续上下文检索
      position: {
        chunk_id: r.chunk_id,
        md_file_path: r.md_file_path,
        file_order_in_book: r.file_order_in_book,
        global_index: r.global_chunk_index,
        file_position: `${r.chunk_order_in_file + 1}/${r.total_chunks_in_file}`,
      },
    }));

    // 可选的格式化输出（保持向后兼容）
    let formattedText = "";
    if (format) {
      const lines: string[] = [];
      const modeText = searchMode === "vector" ? "向量搜索" :
                      searchMode === "bm25" ? "BM25文本搜索" :
                      `混合搜索(向量${Math.round((vectorWeight ?? 0.7) * 100)}% + BM25${Math.round((bm25Weight ?? 0.3) * 100)}%)`;
      lines.push(`[RAG检索结果] 使用${modeText}找到 ${results.length} 个相关片段，包含位置信息：`);
      lines.push(`💭 调用原因：${reasoning}\n`);

      enhancedContext.forEach((item) => {
        const pos = item.position;
        lines.push(`【${item.rank} | 相似度${item.similarity}%】`);
        lines.push(`章节：${item.related_chapter_titles}`);
        lines.push(`位置：文件-${pos.md_file_path} 第${pos.file_position}块 (文件顺序${pos.file_order_in_book}, 全局${pos.global_index})`);

        // 控制片段长度
        const snippet = item.content.length > 800 ? `${item.content.slice(0, 800)}…` : item.content;
        lines.push(snippet);
        lines.push("---\n");
      });

      formattedText = lines.join("\n");
    }

    // 生成标准化引用信息
    const citations = enhancedContext.map((item, idx) => ({
      id: idx + 1, // 引用序号 [1], [2] 等
      source: `${item.related_chapter_titles} - 相似度${item.similarity}%`,
      md_file_path: item.position.md_file_path,
      position: `文件-${item.position.md_file_path} 第${item.position.file_position}块`,
      preview: item.content.slice(0, 100) + (item.content.length > 100 ? "..." : ""),
    }));

    // 生成引用提示文本（供 AI 使用）
    const citationGuide = [
      "📚 引用标注指南：",
      "在回答中引用相关信息时，请在句子末尾添加对应的引用标注：",
      ...citations.map((c) => `[${c.id}] ${c.source} (${c.position})`),
      "",
      "示例：「根据书中描述，这个概念很重要[1]。相关原理如下[2]」",
    ].join("\n");

    return {
      // 增强的结构化数据，包含位置信息
      results: enhancedContext,
      // 格式化文本（如果需要）
      formatted: format ? formattedText : null,
      // ✨ 新增：标准化引用信息
      citations: citations,
      // ✨ 新增：引用指南（供 AI 参考）
      citation_guide: citationGuide,
      // 元信息
      meta: {
        reasoning,
        total_found: results.length,
        book_id: activeBookId,
        query: question,
        search_config: {
          mode: searchMode ?? "hybrid",
          vector_weight: vectorWeight ?? 0.7,
          bm25_weight: bm25Weight ?? 0.3,
        },
      },
    };
  },
});
