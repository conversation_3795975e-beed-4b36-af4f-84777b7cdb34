import { useActiveBookStore } from "@/store/activeBookStore";
import { invoke } from "@tauri-apps/api/core";
import { tool } from "ai";
import { z } from "zod";

// 文档分块类型
type DocumentChunk = {
  id?: number;
  book_title: string;
  book_author: string;
  md_file_path: string;
  file_order_in_book: number;
  related_chapter_titles: string;
  chunk_text: string;
  chunk_order_in_file: number;
  total_chunks_in_file: number;
  global_chunk_index: number;
};

// RAG上下文检索工具：基于chunk_id获取前后文
export const ragContextTool = tool({
  description: "基于分块ID获取该分块的前后文内容，用于扩展上下文信息",
  inputSchema: z.object({
    reasoning: z.string().min(1).describe("调用此工具的原因和目的，例如：'需要获取更多上下文来理解用户问题'"),
    chunk_id: z.number().int().min(1).describe("目标分块的数据库ID"),
    prev_count: z.number().int().min(0).max(10).default(2).describe("获取前面多少个分块，默认2个"),
    next_count: z.number().int().min(0).max(10).default(2).describe("获取后面多少个分块，默认2个"),
  }),
  execute: async ({
    reasoning,
    chunk_id,
    prev_count,
    next_count,
  }: {
    reasoning: string;
    chunk_id: number;
    prev_count?: number;
    next_count?: number;
  }) => {
    const { activeBookId } = useActiveBookStore.getState();
    if (!activeBookId) {
      throw new Error("未找到当前阅读图书，请先在阅读器中打开图书");
    }

    const results = (await invoke("plugin:epub|get_chunk_with_context", {
      bookId: activeBookId,
      chunkId: chunk_id,
      prevCount: prev_count ?? 2,
      nextCount: next_count ?? 2,
    })) as DocumentChunk[];

    // 找到目标分块的位置
    const targetIndex = results.findIndex((chunk) => chunk.id === chunk_id);

    // 构建上下文信息
    const contextData = results.map((chunk, index) => {
      const isTarget = chunk.id === chunk_id;
      const relativePosition = index - targetIndex;

      return {
        // 基础信息
        chunk_id: chunk.id,
        chapter_title: chunk.related_chapter_titles,
        content: chunk.chunk_text,

        // 位置标识
        is_target: isTarget,
        relative_position: relativePosition, // -2, -1, 0, 1, 2
        position_label:
          relativePosition === 0
            ? "目标分块"
            : relativePosition < 0
              ? `前${Math.abs(relativePosition)}个`
              : `后${relativePosition}个`,

        // 详细位置信息
        toc_info: {
          global_index: chunk.global_chunk_index,
          md_source: chunk.md_file_path,
          position_in_file: `${chunk.chunk_order_in_file + 1}/${chunk.total_chunks_in_file}`,
          file_order: chunk.file_order_in_book,
        },
      };
    });

    // 格式化输出
    const lines: string[] = [];
    lines.push(`[上下文检索] 分块ID ${chunk_id} 的前后文内容：`);
    lines.push(`💭 调用原因：${reasoning}\n`);

    contextData.forEach((item) => {
      const indicator = item.is_target ? "🎯" : "📄";
      lines.push(`${indicator} ${item.position_label} | ${item.chapter_title}`);
      lines.push(`   位置：${item.toc_info.position_in_file} (全局${item.toc_info.global_index})`);
      lines.push(`   内容：${item.content.slice(0, 200)}${item.content.length > 200 ? "..." : ""}`);
      lines.push("");
    });

    // 生成上下文引用信息
    const citations = contextData.map((item, idx) => ({
      id: idx + 1,
      source: `${item.chapter_title}${item.is_target ? " (目标块)" : " (上下文)"}`,
      file_path: item.toc_info.md_source,
      position: `${item.position_label} - ${item.toc_info.position_in_file}`,
      preview: item.content.slice(0, 100) + (item.content.length > 100 ? "..." : ""),
      is_target: item.is_target,
    }));

    // 生成引用指南
    const citationGuide = [
      "📚 上下文引用标注指南：",
      "在回答中引用上下文信息时，请使用以下标注：",
      ...citations.map((c) => `[${c.id}] ${c.source} (${c.position})`),
      "",
      "注意：目标块包含核心信息，上下文块提供补充说明",
      "示例：「根据核心内容[1]，结合前文背景[2]...」",
    ].join("\n");

    return {
      // 结构化的上下文数据
      context: contextData,
      // 格式化文本
      formatted: lines.join("\n"),
      // ✨ 新增：标准化引用信息
      citations: citations,
      // ✨ 新增：引用指南
      citation_guide: citationGuide,
      // 元信息
      meta: {
        reasoning,
        target_chunk_id: chunk_id,
        total_chunks: results.length,
        prev_count: prev_count ?? 2,
        next_count: next_count ?? 2,
        target_found: targetIndex >= 0,
      },
    };
  },
});
