import { useActiveBookStore } from "@/store/activeBookStore";
import { invoke } from "@tauri-apps/api/core";
import { tool } from "ai";
import { z } from "zod";

// 文档分块类型
type DocumentChunk = {
  id?: number;
  book_title: string;
  book_author: string;
  md_file_path: string;
  file_order_in_book: number;
  related_chapter_titles: string;
  chunk_text: string;
  chunk_order_in_file: number;
  total_chunks_in_file: number;
  global_chunk_index: number;
};

// RAG章节检索工具：基于章节标题获取整个章节内容
export const ragTocTool = tool({
  description: "基于章节标题获取该章节的完整内容，按文件内顺序返回所有分块",
  inputSchema: z.object({
    reasoning: z.string().min(1).describe("调用此工具的原因和目的，例如：'用户想了解整个章节的内容'"),
    chapter_title: z.string().min(1).describe("章节标题，如 '第一章 引言', '1.1 背景介绍' 等"),
  }),
  execute: async ({
    reasoning,
    chapter_title,
  }: {
    reasoning: string;
    chapter_title: string;
  }) => {
    const { activeBookId } = useActiveBookStore.getState();
    if (!activeBookId) {
      throw new Error("未找到当前阅读图书，请先在阅读器中打开图书");
    }

    const results = (await invoke("plugin:epub|get_toc_chunks", {
      bookId: activeBookId,
      chapterTitle: chapter_title,
    })) as DocumentChunk[];

    if (results.length === 0) {
      throw new Error(`未找到章节 "${chapter_title}" 的内容`);
    }

    // 构建章节信息
    const chapterInfo = {
      chapter_title: chapter_title,
      related_chapter_titles: results[0].related_chapter_titles,
      total_chunks: results.length,
      md_file_path: results[0].md_file_path,
      file_order_in_book: results[0].file_order_in_book,
    };

    // 处理每个分块
    const chapterContent = results.map((chunk, index) => ({
      // 基础信息
      chunk_id: chunk.id,
      sequence: index + 1, // 在文件中的序号
      content: chunk.chunk_text,

      // 位置信息
      position: {
        in_file: `${chunk.chunk_order_in_file + 1}/${chunk.total_chunks_in_file}`,
        global_index: chunk.global_chunk_index,
        is_first: chunk.chunk_order_in_file === 0,
        is_last: chunk.chunk_order_in_file === chunk.total_chunks_in_file - 1,
      },
    }));

    // 格式化输出
    const lines: string[] = [];
    lines.push(`[章节内容] ${chapterInfo.chapter_title}`);
    lines.push(`💭 调用原因：${reasoning}`);
    lines.push(
      `📖 文件顺序：${chapterInfo.file_order_in_book} | 分块数：${chapterInfo.total_chunks} | 来源：${chapterInfo.md_file_path}`,
    );
    lines.push(`📚 相关章节：${chapterInfo.related_chapter_titles}\n`);

    chapterContent.forEach((item) => {
      const isFirstOrLast = item.position.is_first || item.position.is_last;
      const indicator = isFirstOrLast ? "📌" : "📄";
      const label = item.position.is_first ? " [文件开始]" : item.position.is_last ? " [文件结束]" : "";

      lines.push(`${indicator} 第${item.sequence}块 ${label}`);
      lines.push(`   位置：${item.position.in_file} (全局${item.position.global_index})`);
      lines.push(`   内容：${item.content.slice(0, 300)}${item.content.length > 300 ? "..." : ""}`);
      lines.push("");
    });

    // 计算内容统计
    const totalLength = chapterContent.reduce((sum, item) => sum + item.content.length, 0);
    const avgLength = Math.round(totalLength / chapterContent.length);

    // 生成章节引用信息
    const citation = {
      id: 1, // 章节作为一个整体引用
      source: `${chapterInfo.chapter_title}`,
      chapter_title: chapter_title,
      position: `完整章节 (${chapterInfo.total_chunks}块)`,
      summary: `包含${chapterInfo.total_chunks}个内容块，总计${totalLength}字符`,
    };

    // 生成引用指南
    const citationGuide = [
      "📚 章节引用标注指南：",
      "在回答中引用此章节信息时，请使用以下标注：",
      `[1] ${citation.source} - ${citation.position}`,
      "",
      "示例：「根据该章节的描述[1]...」",
    ].join("\n");

    return {
      // 章节基本信息
      chapter: chapterInfo,
      // 完整的章节内容
      content: chapterContent,
      // 格式化文本
      formatted: lines.join("\n"),
      // ✨ 新增：标准化引用信息
      citations: [citation],
      // ✨ 新增：引用指南
      citation_guide: citationGuide,
      // 统计信息
      stats: {
        total_chunks: results.length,
        total_characters: totalLength,
        average_chunk_length: avgLength,
        first_chunk_id: chapterContent[0]?.chunk_id,
        last_chunk_id: chapterContent[chapterContent.length - 1]?.chunk_id,
      },
      // 元信息
      meta: {
        reasoning,
      },
    };
  },
});
