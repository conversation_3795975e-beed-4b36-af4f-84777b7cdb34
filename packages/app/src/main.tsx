import { Toaster } from "@/components/ui/sonner";
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { HashRouter } from "react-router";
import Layout from "./components/Layout.tsx";
import { EnvProvider } from "./context/EnvContext.tsx";
import { TabsProvider } from "./context/TabsContext.tsx";
import { flushAllWrites } from "./libs/tauriStorage.ts";

import "./index.css";

window.addEventListener("beforeunload", () => {
  flushAllWrites().catch((error) => {
    console.error("Failed to flush writes on app close:", error);
  });
});

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <EnvProvider>
      <HashRouter>
        <TabsProvider>
          <Layout />
        </TabsProvider>
      </HashRouter>
      <Toaster position="top-center" />
    </EnvProvider>
  </StrictMode>,
);
