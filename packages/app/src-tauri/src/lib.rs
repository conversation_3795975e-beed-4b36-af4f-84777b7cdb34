// Learn more about <PERSON><PERSON> commands at https://tauri.app/develop/calling-rust/

mod core;
use crate::core::{
    books::commands::{
        delete_book, get_book_by_id, get_book_status, get_books, get_books_with_status, save_book,
        update_book, update_book_status,
    },
    database,
    state::AppState,
    tags::commands::{
        create_tag, delete_tag, get_tag_by_id, get_tag_by_name, get_tags, update_tag,
    },
    threads::commands::{create_thread, edit_thread, get_latest_thread_by_book_key},
    llama::commands::{
        greet,
        get_app_data_dir,
        get_llamacpp_backend_path,
        ensure_llamacpp_directories,
        download_llama_server,
        llama_server_binary_name_cmd,
    },
};
use tauri::Manager;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .manage(AppState::default())
        .plugin(tauri_plugin_sql::Builder::new().build())
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_llamacpp::init())
        .plugin(tauri_plugin_log::Builder::default().level(log::LevelFilter::Info).build())
        .plugin(tauri_plugin_epub::init())
        .setup(|app| {
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                let pool = database::initialize(&app_handle)
                    .await
                    .expect("Failed to initialize database");

                let state = app_handle.state::<AppState>();
                let mut db_pool_guard = state.db_pool.lock().await;
                *db_pool_guard = Some(pool);
            });
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            create_thread,
            edit_thread,
            get_latest_thread_by_book_key,
            save_book,
            get_books,
            get_book_by_id,
            update_book,
            delete_book,
            get_book_status,
            update_book_status,
            get_books_with_status,
            create_tag,
            get_tags,
            get_tag_by_id,
            get_tag_by_name,
            update_tag,
            delete_tag,
            // llama
            greet,
            get_app_data_dir,
            get_llamacpp_backend_path,
            ensure_llamacpp_directories,
            download_llama_server,
            llama_server_binary_name_cmd,
        ])
        .on_window_event(|window, event| {
            if let tauri::WindowEvent::CloseRequested { .. } = event {
                let app_handle = window.app_handle().clone();
                tauri::async_runtime::spawn(async move {
                    if let Err(e) = tauri_plugin_llamacpp::cleanup_llama_processes(app_handle).await
                    {
                        log::error!("清理 llamacpp 进程失败: {}", e);
                    }
                });
            }
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
