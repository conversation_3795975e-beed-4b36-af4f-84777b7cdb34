use super::models::{EditThreadPayload, NewThreadPayload, Thread};
use crate::core::state::AppState;
use sqlx::Row;
use tauri::State;
use uuid::Uuid;

#[tauri::command]
pub async fn create_thread(
    payload: NewThreadPayload,
    state: State<'_, AppState>,
) -> Result<Thread, String> {
    let thread_id = Uuid::new_v4().to_string();
    let current_timestamp = chrono::Utc::now().timestamp();

    let db_pool_guard = state.db_pool.lock().await;
    let pool = db_pool_guard.as_ref().ok_or("Database not initialized")?;

    sqlx::query(
        "INSERT INTO threads (id, book_key, metadata, title, messages, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
    )
    .bind(&thread_id)
    .bind(&payload.book_key)
    .bind(&payload.metadata)
    .bind(&payload.title)
    .bind(&payload.messages_json)
    .bind(current_timestamp)
    .bind(current_timestamp)
    .execute(pool)
    .await
    .map_err(|e| {
        eprintln!("Failed to create thread: {}", e);
        e.to_string()
    })?;

    let new_thread = Thread {
        id: thread_id,
        book_key: payload.book_key,
        metadata: payload.metadata,
        title: payload.title,
        messages: payload.messages_json,
        created_at: current_timestamp,
        updated_at: current_timestamp,
    };
    Ok(new_thread)
}

#[tauri::command]
pub async fn edit_thread(
    payload: EditThreadPayload,
    state: State<'_, AppState>,
) -> Result<Thread, String> {
    let current_timestamp = chrono::Utc::now().timestamp();

    let db_pool_guard = state.db_pool.lock().await;
    let pool = db_pool_guard.as_ref().ok_or("Database not initialized")?;

    let row = sqlx::query(
        "SELECT id, book_key, metadata, title, messages, created_at, updated_at FROM threads WHERE id = ?"
    )
    .bind(&payload.id)
    .fetch_one(pool)
    .await
    .map_err(|e| {
        eprintln!("Failed to fetch existing thread: {}", e);
        "Thread not found".to_string()
    })?;

    let existing_thread = Thread {
        id: row.get("id"),
        book_key: row.get("book_key"),
        metadata: row.get("metadata"),
        title: row.get("title"),
        messages: row.get("messages"),
        created_at: row.get("created_at"),
        updated_at: row.get("updated_at"),
    };

    let new_title = payload.title.unwrap_or(existing_thread.title);
    let new_metadata = payload.metadata.unwrap_or(existing_thread.metadata);
    let new_messages = payload.messages_json.unwrap_or(existing_thread.messages);

    sqlx::query(
        "UPDATE threads SET title = ?, metadata = ?, messages = ?, updated_at = ? WHERE id = ?",
    )
    .bind(&new_title)
    .bind(&new_metadata)
    .bind(&new_messages)
    .bind(current_timestamp)
    .bind(&payload.id)
    .execute(pool)
    .await
    .map_err(|e| {
        eprintln!("Failed to update thread: {}", e);
        e.to_string()
    })?;

    let updated_thread = Thread {
        id: existing_thread.id,
        book_key: existing_thread.book_key,
        metadata: new_metadata,
        title: new_title,
        messages: new_messages,
        created_at: existing_thread.created_at,
        updated_at: current_timestamp,
    };

    Ok(updated_thread)
}

#[tauri::command]
pub async fn get_latest_thread_by_book_key(
    book_key: String,
    state: State<'_, AppState>,
) -> Result<Option<Thread>, String> {
    let db_pool_guard = state.db_pool.lock().await;
    let pool = db_pool_guard.as_ref().ok_or("Database not initialized")?;

    let row_result = sqlx::query(
        "SELECT id, book_key, metadata, title, messages, created_at, updated_at FROM threads WHERE book_key = ? ORDER BY updated_at DESC LIMIT 1"
    )
    .bind(&book_key)
    .fetch_optional(pool)
    .await
    .map_err(|e| {
        eprintln!("Failed to fetch latest thread by book_key: {}", e);
        e.to_string()
    })?;

    if let Some(row) = row_result {
        let thread = Thread {
            id: row.get("id"),
            book_key: row.get("book_key"),
            metadata: row.get("metadata"),
            title: row.get("title"),
            messages: row.get("messages"),
            created_at: row.get("created_at"),
            updated_at: row.get("updated_at"),
        };
        Ok(Some(thread))
    } else {
        Ok(None)
    }
}
